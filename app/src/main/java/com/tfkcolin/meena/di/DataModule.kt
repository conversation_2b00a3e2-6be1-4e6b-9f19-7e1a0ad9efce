package com.tfkcolin.meena.di

import android.content.Context
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.tfkcolin.meena.data.api.AuthApi
import com.tfkcolin.meena.data.api.ChatApi
import com.tfkcolin.meena.data.api.ContactApi
import com.tfkcolin.meena.data.local.AppDatabase
import com.tfkcolin.meena.data.local.ChatDao
import com.tfkcolin.meena.data.local.ContactDao
import com.tfkcolin.meena.data.local.ContactGroupDao
import com.tfkcolin.meena.data.local.MediaAttachmentDao
import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.data.local.UserDao
import com.tfkcolin.meena.data.repositories.AuthRepository
import com.tfkcolin.meena.data.repositories.ChatRepository
import com.tfkcolin.meena.data.repositories.ContactRepository
import com.tfkcolin.meena.data.repositories.UserRepository
import com.tfkcolin.meena.data.repository.ContactGroupRepository
import com.tfkcolin.meena.data.repository.IContactGroupRepository
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import com.tfkcolin.meena.domain.repositories.IChatRepository
import com.tfkcolin.meena.domain.repositories.IContactRepository
import com.tfkcolin.meena.domain.repositories.IUserRepository
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing data layer dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    /**
     * Bind the auth repository implementation to the interface.
     */
    @Binds
    @Singleton
    abstract fun bindAuthRepository(authRepository: AuthRepository): IAuthRepository

    /**
     * Bind the contact repository implementation to the interface.
     */
    @Binds
    @Singleton
    abstract fun bindContactRepository(contactRepository: ContactRepository): IContactRepository

    /**
     * Bind the chat repository implementation to the interface.
     */
    @Binds
    @Singleton
    abstract fun bindChatRepository(chatRepository: ChatRepository): IChatRepository

    /**
     * Bind the user repository implementation to the interface.
     */
    @Binds
    @Singleton
    abstract fun bindUserRepository(userRepository: UserRepository): IUserRepository

    /**
     * Bind the contact group repository implementation to the interface.
     */
    @Binds
    @Singleton
    abstract fun bindContactGroupRepository(contactGroupRepository: ContactGroupRepository): IContactGroupRepository

}

/**
 * Hilt module for providing data layer dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
object DataModule {

    /**
     * Provide the Room database.
     */
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return AppDatabase.getInstance(context)
    }

    /**
     * Provide the user DAO.
     */
    @Provides
    @Singleton
    fun provideUserDao(appDatabase: AppDatabase): UserDao {
        return appDatabase.userDao()
    }

    /**
     * Provide the contact DAO.
     */
    @Provides
    @Singleton
    fun provideContactDao(appDatabase: AppDatabase): ContactDao {
        return appDatabase.contactDao()
    }

    /**
     * Provide the message DAO.
     */
    @Provides
    @Singleton
    fun provideMessageDao(appDatabase: AppDatabase): MessageDao {
        return appDatabase.messageDao()
    }

    /**
     * Provide the chat DAO.
     */
    @Provides
    @Singleton
    fun provideChatDao(appDatabase: AppDatabase): ChatDao {
        return appDatabase.chatDao()
    }

    /**
     * Provide the media attachment DAO.
     */
    @Provides
    @Singleton
    fun provideMediaAttachmentDao(appDatabase: AppDatabase): MediaAttachmentDao {
        return appDatabase.mediaAttachmentDao()
    }

    /**
     * Provide the contact group DAO.
     */
    @Provides
    @Singleton
    fun provideContactGroupDao(appDatabase: AppDatabase): ContactGroupDao {
        return appDatabase.contactGroupDao()
    }

    /**
     * Provide the Gson instance.
     */
    @Provides
    @Singleton
    fun provideGson(): Gson {
        return GsonBuilder()
            .setLenient()
            .create()
    }
}
