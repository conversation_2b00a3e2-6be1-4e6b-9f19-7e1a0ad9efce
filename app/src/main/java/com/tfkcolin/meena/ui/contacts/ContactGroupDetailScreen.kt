package com.tfkcolin.meena.ui.contacts

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Group
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FabPosition
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.components.MeenaSecondaryButton
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.contacts.components.ContactListItem

/**
 * Contact group detail screen.
 *
 * @param groupId The ID of the group to display.
 * @param onNavigateBack Navigate back to the groups screen.
 * @param onNavigateToContactDetail Navigate to the contact detail screen.
 * @param viewModel The contacts view model.
 */
@Composable
fun ContactGroupDetailScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onShowSnackbar: (String) -> Unit,
    onSetFab: (@Composable (() -> Unit)?, FabPosition) -> Unit,
    groupId: String,
    onNavigateBack: () -> Unit,
    onNavigateToContactDetail: (String) -> Unit,
    viewModel: ContactsViewModel = hiltViewModel()
) {
    val contactsState by viewModel.contactsState.collectAsState()
    val contactGroups by viewModel.contactGroups.collectAsState()
    val contacts by viewModel.contacts.collectAsState()

    // Find the group with the given ID
    val group = contactGroups.find { it.id == groupId }

    // Get the contacts in this group
    val groupContacts = if (group != null) {
        contacts.filter { it.id in group.contactIds }
    } else {
        emptyList()
    }

    // Get the contacts not in this group
    val nonGroupContacts = if (group != null) {
        contacts.filter { it.id !in group.contactIds }
    } else {
        emptyList()
    }

    var showEditGroupDialog by remember { mutableStateOf(false) }
    var showDeleteGroupDialog by remember { mutableStateOf(false) }
    var showAddMemberDialog by remember { mutableStateOf(false) }
    var groupName by remember { mutableStateOf(group?.name ?: "") }
    var selectedContact by remember { mutableStateOf<Contact?>(null) }

    // Show error message
    LaunchedEffect(contactsState.error) {
        contactsState.error?.let {
            onShowSnackbar(it)
            viewModel.clearError()
        }
    }

    val groupUpdatedSuccessfully = stringResource(R.string.group_updated_successfully)
    val groupDeletedSuccessfully = stringResource(R.string.group_deleted_successfully)
    val memberAddedToGroup = stringResource(R.string.member_added_to_group)
    val memberRemovedFromGroup = stringResource(R.string.member_removed_from_group)

    // Show operation success/error messages
    LaunchedEffect(contactsState.updateGroupOperation.isSuccessful) {
        if (contactsState.updateGroupOperation.isSuccessful) {
            onShowSnackbar(groupUpdatedSuccessfully)
            viewModel.resetContactOperationStates()
        }
    }
    LaunchedEffect(contactsState.deleteGroupOperation.isSuccessful) {
        if (contactsState.deleteGroupOperation.isSuccessful) {
            onShowSnackbar(groupDeletedSuccessfully)
            viewModel.resetContactOperationStates()
            onNavigateBack()
        }
    }
    LaunchedEffect(contactsState.addToGroupOperation.isSuccessful) {
        if (contactsState.addToGroupOperation.isSuccessful) {
            onShowSnackbar(memberAddedToGroup)
            viewModel.resetContactOperationStates()
        }
    }
    LaunchedEffect(contactsState.removeFromGroupOperation.isSuccessful) {
        if (contactsState.removeFromGroupOperation.isSuccessful) {
            onShowSnackbar(memberRemovedFromGroup)
            viewModel.resetContactOperationStates()
        }
    }

    // Edit group dialog
    if (showEditGroupDialog) {
        AlertDialog(
            onDismissRequest = {
                showEditGroupDialog = false
                groupName = group?.name ?: ""
            },
            title = { Text(stringResource(R.string.edit_group)) },
            text = {
                Column {
                    Text(stringResource(R.string.enter_new_group_name))
                    Spacer(modifier = Modifier.height(8.dp))
                    TextField(
                        value = groupName,
                        onValueChange = { groupName = it },
                        label = { Text(stringResource(R.string.group_name_label)) },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (groupName.isNotBlank() && group != null) {
                            viewModel.updateContactGroup(group.id, groupName)
                            showEditGroupDialog = false
                        }
                    },
                    enabled = groupName.isNotBlank()
                ) {
                    Text(stringResource(R.string.save_button))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showEditGroupDialog = false
                        groupName = group?.name ?: ""
                    }
                ) {
                    Text(stringResource(R.string.cancel_button))
                }
            }
        )
    }

    // Delete group dialog
    if (showDeleteGroupDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteGroupDialog = false },
            title = { Text(stringResource(R.string.delete_group)) },
            text = { Text(stringResource(R.string.confirm_delete_group_message)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (group != null) {
                            viewModel.deleteContactGroup(group.id)
                            showDeleteGroupDialog = false
                        }
                    }
                ) {
                    Text(stringResource(R.string.delete_button))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteGroupDialog = false }
                ) {
                    Text(stringResource(R.string.cancel_button))
                }
            }
        )
    }

    // Add member dialog
    if (showAddMemberDialog) {
        AlertDialog(
            onDismissRequest = {
                showAddMemberDialog = false
                selectedContact = null
            },
            title = { Text(stringResource(R.string.add_member)) },
            text = {
                Column {
                    Text(stringResource(R.string.select_contact_to_add))
                    Spacer(modifier = Modifier.height(8.dp))
                    if (nonGroupContacts.isEmpty()) {
                        Text(
                            text = stringResource(R.string.all_contacts_in_group),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    } else {
                        LazyColumn(
                            modifier = Modifier.height(300.dp)
                        ) {
                            items(nonGroupContacts) { contact ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 8.dp)
                                        .clickable { selectedContact = contact },
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = contact.displayName ?: "User",
                                        style = MaterialTheme.typography.bodyLarge,
                                        modifier = Modifier.weight(1f)
                                    )
                                    if (selectedContact?.id == contact.id) {
                                        Icon(
                                            imageVector = Icons.Default.Add,
                                            contentDescription = null,
                                            tint = MaterialTheme.colorScheme.primary
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (group != null && selectedContact != null) {
                            viewModel.addContactToGroup(group.id, selectedContact!!.id)
                            showAddMemberDialog = false
                            selectedContact = null
                        }
                    },
                    enabled = selectedContact != null
                ) {
                    Text(stringResource(R.string.add_button))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showAddMemberDialog = false
                        selectedContact = null
                    }
                ) {
                    Text(stringResource(R.string.cancel_button))
                }
            }
        )
    }

    DisposableEffect(Unit) {
        onSetTopAppBar {
            MeenaTopBar(
                title = group?.name ?: stringResource(R.string.group_details),
                onBackClick = onNavigateBack,
                actions = {
                    IconButton(onClick = { showEditGroupDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = stringResource(R.string.edit_group)
                        )
                    }
                    IconButton(onClick = { showDeleteGroupDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = stringResource(R.string.delete_group)
                        )
                    }
                }
            )
        }
        onSetFab(
            {
                FloatingActionButton(
                    onClick = { showAddMemberDialog = true },
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = Color.White
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(R.string.add_member)
                    )
                }
            }, FabPosition.End)
        onDispose {
            onSetTopAppBar(null)
            onSetFab(null, FabPosition.End)
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        if (group == null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(R.string.group_not_found),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                MeenaPrimaryButton(
                    text = stringResource(R.string.go_back_button),
                    onClick = onNavigateBack
                )
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .padding(end = 16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Group,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(32.dp)
                        )
                    }
                    Column {
                        Text(
                            text = group.name,
                            style = MaterialTheme.typography.titleLarge.copy(
                                fontWeight = FontWeight.Bold
                            )
                        )
                        Text(
                            text = "${group.contactCount} ${stringResource(R.string.members_title)}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    MeenaPrimaryButton(
                        text = stringResource(R.string.message_group_button),
                        onClick = { /* TODO */ },
                        modifier = Modifier.weight(1f)
                    )
                    MeenaSecondaryButton(
                        text = stringResource(R.string.share_group_button),
                        onClick = { /* TODO */ },
                        modifier = Modifier.weight(1f)
                    )
                }
                Spacer(modifier = Modifier.height(24.dp))
                Text(
                    text = stringResource(R.string.members_title),
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    )
                )
                Spacer(modifier = Modifier.height(8.dp))
                if (groupContacts.isEmpty()) {
                    Text(
                        text = stringResource(R.string.no_members_in_group),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                } else {
                    LazyColumn(
                        contentPadding = PaddingValues(bottom = 80.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(groupContacts) { contact ->
                            ContactListItem(
                                contact = contact,
                                onClick = { onNavigateToContactDetail(contact.id) },
                                onRemove = {
                                    viewModel.removeContactFromGroup(group.id, contact.id)
                                }
                            )
                        }
                    }
                }
            }
        }
        if (contactsState.properties.isLoading ||
            contactsState.updateGroupOperation.isInProgress ||
            contactsState.deleteGroupOperation.isInProgress ||
            contactsState.addToGroupOperation.isInProgress ||
            contactsState.removeFromGroupOperation.isInProgress) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}