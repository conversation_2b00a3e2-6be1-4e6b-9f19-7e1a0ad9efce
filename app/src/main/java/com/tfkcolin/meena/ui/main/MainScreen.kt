package com.tfkcolin.meena.ui.main

import androidx.compose.material3.FabPosition
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.runtime.getValue
import androidx.compose.ui.geometry.Offset
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.tfkcolin.meena.ui.chat.ChatListScreen
import com.tfkcolin.meena.ui.chat.ChatScreen
import com.tfkcolin.meena.ui.chat.ChatSearchScreen
import com.tfkcolin.meena.ui.chat.ChatViewModel
import com.tfkcolin.meena.ui.chat.ForwardMessageScreen
import com.tfkcolin.meena.ui.chat.NewChatScreen
import com.tfkcolin.meena.ui.chat.SearchScreen
import com.tfkcolin.meena.ui.components.MediaPreviewScreen
import com.tfkcolin.meena.ui.contacts.AddContactScreen
import com.tfkcolin.meena.ui.contacts.ContactDetailScreen
import com.tfkcolin.meena.ui.contacts.ContactGroupDetailScreen
import com.tfkcolin.meena.ui.contacts.ContactGroupsScreen
import com.tfkcolin.meena.ui.contacts.ContactListScreen
import com.tfkcolin.meena.ui.contacts.ContactsViewModel
import com.tfkcolin.meena.ui.contacts.EditContactScreen
import com.tfkcolin.meena.ui.group.GroupCreationScreen
import com.tfkcolin.meena.ui.group.GroupDetailsScreen
import com.tfkcolin.meena.ui.navigation.Screen
import com.tfkcolin.meena.ui.profile.EditProfileScreen
import com.tfkcolin.meena.ui.profile.ProfileScreen
import com.tfkcolin.meena.ui.profile.SecuritySettingsScreen
import com.tfkcolin.meena.ui.settings.AppearanceSettingsScreen
import com.tfkcolin.meena.ui.settings.NotificationsSettingsScreen
import com.tfkcolin.meena.ui.settings.LanguageSettingsScreen
import com.tfkcolin.meena.ui.stories.StoriesScreen
import com.tfkcolin.meena.ui.stories.StoryCreationScreen
import com.tfkcolin.meena.ui.stories.StoryViewerScreen
import com.tfkcolin.meena.ui.stories.models.OverlayItem
import com.tfkcolin.meena.ui.stories.models.OverlayType
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.navigation
import com.tfkcolin.meena.ui.profile.ProfileViewModel
import com.tfkcolin.meena.ui.settings.SettingsViewModel
import com.tfkcolin.meena.ui.stories.models.SegmentType
import com.tfkcolin.meena.ui.stories.models.Story
import com.tfkcolin.meena.ui.stories.models.StorySegment
import com.tfkcolin.meena.ui.stories.viewmodel.StoryViewModel
import com.tfkcolin.meena.ui.viewmodels.MediaViewModel
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.ui.calls.CallListScreen
import com.tfkcolin.meena.ui.calls.Call
import com.tfkcolin.meena.ui.calls.CallType
import com.tfkcolin.meena.ui.components.BottomNavBar
import com.tfkcolin.meena.ui.navigation.MAIN_GRAPH_ROUTE
import java.util.Date

/**
 * Main navigation graph.
 * This handles all main screen navigation.
 */
fun NavGraphBuilder.mainNavigation(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetBottomAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetFab: (@Composable (() -> Unit)?, FabPosition) -> Unit,
    onShowSnackbar: (String) -> Unit,
    navController: NavHostController,
    onLogout: () -> Unit,
    contactsViewModel: ContactsViewModel,
    chatViewModel: ChatViewModel
) {
    navigation(
        startDestination = Screen.Chats.route,
        route = MAIN_GRAPH_ROUTE
    ) {
        composable(Screen.Chats.route) {
            // Check authentication for this screen
            val screenContext = LocalContext.current
            val screenTokenManager = remember { TokenManager(screenContext) }

            if (!screenTokenManager.isLoggedIn()) {
                LaunchedEffect(Unit) {
                    onLogout()
                }
                return@composable
            }
            DisposableEffect (Unit) {
                onSetBottomAppBar {
                    BottomNavBar(
                        navController = navController,
                    )
                }
                onDispose {
                    onSetBottomAppBar(null)
                }
            }
            ChatListScreen(
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onSetFab = onSetFab,
                onShowSnackbar = onShowSnackbar,
                onNavigateToChat = { chatId ->
                    // For chat list, we use a default recipient ID since it's not needed
                    // The actual recipient ID will be determined in the chat screen
                    navController.navigate(Screen.Chat.createRoute(chatId, "default"))
                },
                onNavigateToNewChat = {
                    navController.navigate(Screen.NewChat.route)
                },
                onNavigateToSearch = {
                    navController.navigate(Screen.Search.route)
                },
                onNavigateToCreateGroup = {
                    navController.navigate(Screen.CreateGroup.route)
                },
                chatUiState = chatViewModel.uiState.collectAsState().value,
                chats = chatViewModel.chats.collectAsState().value,
                conversationListItems = chatViewModel.conversationListItems.collectAsState().value,
                onClearChatError = chatViewModel::clearError,
                onArchiveChat = chatViewModel::archiveChat,
                onMuteChat = chatViewModel::muteChat,
                onPinChat = chatViewModel::pinChat,
                onToggleShowArchivedChats = chatViewModel::toggleShowArchivedChats,
                onLoadChats = chatViewModel::loadChats,
                getOneToOneConversations = chatViewModel::getOneToOneConversations,
                getGroupConversations = chatViewModel::getGroupConversations,
                getChannelConversations = chatViewModel::getChannelConversations
            )
        }

        // Chat screens
        composable(
            route = Screen.Chat.route,
            arguments = listOf(
                navArgument("chatId") { type = NavType.StringType },
                navArgument("recipientId") { type = NavType.StringType },
            )
        ) { backStackEntry ->
            val chatId = backStackEntry.arguments?.getString("chatId") ?: ""
            val recipientId = backStackEntry.arguments?.getString("recipientId") ?: ""
            ChatScreen(
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onShowSnackbar = onShowSnackbar,
                chatId = chatId,
                recipientId = recipientId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToSearch = { chatId, recipientName ->
                    navController.navigate(Screen.ChatSearch.createRoute(chatId, recipientName))
                },
                onNavigateToForward = { messageId ->
                    navController.navigate(Screen.ForwardMessage.createRoute(messageId))
                },
                navController = navController,
                chatUiState = chatViewModel.uiState.collectAsState().value,
                messages = chatViewModel.messages.collectAsState().value,
                typingUsers = chatViewModel.typingUsers.collectAsState().value,
                mediaAttachmentHelper = chatViewModel.mediaAttachmentHelper,
                conversationListItems = chatViewModel.conversationListItems.collectAsState().value,
                onSendTypingIndicator = chatViewModel::sendTypingIndicator,
                onSetCurrentChat = chatViewModel::setCurrentChat,
                onClearChatError = chatViewModel::clearError,
                onResetAllOperationStates = chatViewModel::resetAllOperationStates,
                onClearMessageToEdit = chatViewModel::clearMessageToEdit,
                onEditMessage = chatViewModel::editMessage,
                onDeleteMessageForSelf = chatViewModel::deleteMessageForSelf,
                onDeleteMessageForEveryone = chatViewModel::deleteMessageForEveryone,
                onGetCurrentUserId = chatViewModel::getCurrentUserId,
                onSetMessageToEdit = chatViewModel::setMessageToEdit,
                onSetMessageToReplyTo = chatViewModel::setMessageToReplyTo,
                onToggleReaction = chatViewModel::toggleReaction,
                onAddAttachment = chatViewModel::addAttachment,
                onRemoveAttachment = chatViewModel::removeAttachment,
                onClearAttachments = chatViewModel::clearAttachments,
                onReplyToMessageWithAttachments = chatViewModel::replyToMessageWithAttachments,
                onReplyToMessage = chatViewModel::replyToMessage,
                onSendMessageWithAttachments = chatViewModel::sendMessageWithAttachments,
                onSendMessage = chatViewModel::sendMessage,
                onGetMessageById = chatViewModel::getMessageById
            )
        }

        composable(Screen.NewChat.route) {
            NewChatScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToChat = { chatId, recipientId ->
                    navController.navigate(Screen.Chat.createRoute(chatId, recipientId)) {
                        popUpTo(Screen.NewChat.route) { inclusive = true }
                    }
                },
                contactsState = contactsViewModel.contactsState.collectAsState().value,
                contacts = contactsViewModel.contacts.collectAsState().value,
                onClearContactsError = contactsViewModel::clearError,
                chatUiState = chatViewModel.uiState.collectAsState().value,
                onGetOrCreateChat = chatViewModel::getOrCreateChat,
                onResetChatOperationStates = chatViewModel::resetAllOperationStates,
                onClearChatError = chatViewModel::clearError
            )
        }

        // Search screens
        composable(Screen.Search.route) {
            val conversationListItems = chatViewModel.conversationListItems.collectAsState().value
            SearchScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToChat = { chatId, recipientId ->
                    navController.navigate(Screen.Chat.createRoute(chatId, recipientId)) {
                        popUpTo(Screen.Search.route) { inclusive = true }
                    }
                },
                chatUiState = chatViewModel.uiState.collectAsState().value,
                onClearChatError = chatViewModel::clearError,
                onClearSearch = chatViewModel::clearSearch,
                onSearchMessages = chatViewModel::searchMessages,
                getChatNameById = { chatId ->
                    conversationListItems
                        .find { it.id == chatId }?.name ?: chatId
                }
            )
        }

        composable(
            route = Screen.ChatSearch.route,
            arguments = listOf(
                navArgument("chatId") { type = NavType.StringType },
                navArgument("recipientName") { type = NavType.StringType },
            )
        ) { backStackEntry ->
            val chatId = backStackEntry.arguments?.getString("chatId") ?: ""
            val recipientName = backStackEntry.arguments?.getString("recipientName") ?: ""
            ChatSearchScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                chatId = chatId,
                recipientName = recipientName,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onMessageClick = { messageId ->
                    //TODO: we would navigate to the specific message
                    // For now, just go back to the chat
                    navController.popBackStack()
                },
                chatUiState = chatViewModel.uiState.collectAsState().value,
                onSetCurrentChat = chatViewModel::setCurrentChat,
                onClearChatError = chatViewModel::clearError,
                onClearSearch = chatViewModel::clearSearch,
                onSearchMessagesInCurrentChat = chatViewModel::searchMessagesInCurrentChat
            )
        }

        // Forward message screen
        composable(
            route = Screen.ForwardMessage.route,
            arguments = listOf(
                navArgument("messageId") {
                    type = NavType.StringType
                }
            )
        ) { backStackEntry ->
            val messageId = backStackEntry.arguments?.getString("messageId") ?: ""
            ForwardMessageScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                messageId = messageId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                chatUiState = chatViewModel.uiState.collectAsState().value,
                conversationListItems = chatViewModel.conversationListItems.collectAsState().value,
                onClearChatError = chatViewModel::clearError,
                onResetAllOperationStates = chatViewModel::resetAllOperationStates,
                onForwardMessage = chatViewModel::forwardMessage
            )
        }

        // Contacts screens
        composable(Screen.Contacts.route) {
            // Check authentication for this screen
            val screenContext = LocalContext.current
            val screenTokenManager = remember { TokenManager(screenContext) }

            if (!screenTokenManager.isLoggedIn()) {
                LaunchedEffect(Unit) {
                    onLogout()
                }
                return@composable
            }

            ContactListScreen(
                onNavigateToAddContact = {
                    navController.navigate(Screen.AddContact.route)
                },
                onNavigateToContactDetail = { contactId ->
                    navController.navigate(Screen.ContactDetail.createRoute(contactId))
                },
                onNavigateToContactGroups = {
                    navController.navigate(Screen.ContactGroups.route)
                },
                contactsState = contactsViewModel.contactsState.collectAsState().value,
                contacts = contactsViewModel.contacts.collectAsState().value,
                onClearContactsError = contactsViewModel::clearError,
                onResetContactOperationStates = contactsViewModel::resetContactOperationStates,
                onLoadContacts = contactsViewModel::loadContacts,
                onSearchContacts = contactsViewModel::searchContacts,
                onFilterContactsByRelationship = contactsViewModel::filterContactsByRelationship,
                onDeleteContact = contactsViewModel::deleteContact,
                onRemoveFromFavorites = contactsViewModel::removeFromFavorites,
                onAddToFavorites = contactsViewModel::addToFavorites,
                onUnblockContact = contactsViewModel::unblockContact,
                onBlockContact = contactsViewModel::blockContact,
                onSetTopAppBar = onSetTopAppBar, // Added this line
                onSetFab = onSetFab, // Added this line
                onShowSnackbar = onShowSnackbar // Added this line
            )
        }

        composable(Screen.AddContact.route) {
            AddContactScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onNavigateBack = {
                    navController.popBackStack()
                },
                contactsState = contactsViewModel.contactsState.collectAsState().value,
                onClearContactsError = contactsViewModel::clearError,
                onResetContactOperationStates = contactsViewModel::resetContactOperationStates,
                onAddContact = contactsViewModel::addContact,
                onUpdateAddContactMeenaId = contactsViewModel::updateAddContactMeenaId
            )
        }

        composable(
            route = Screen.ContactDetail.route,
            arguments = listOf(
                navArgument("contactId") { type = NavType.StringType },
            )
        ) { backStackEntry ->
            val contactId = backStackEntry.arguments?.getString("contactId") ?: ""
            ContactDetailScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                contactId = contactId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToEditContact = { id ->
                    navController.navigate(Screen.EditContact.createRoute(id))
                }
            )
        }

        composable(
            route = Screen.EditContact.route,
            arguments = listOf(
                navArgument("contactId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val contactId = backStackEntry.arguments?.getString("contactId") ?: ""
            EditContactScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                contactId = contactId,
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // Contact Groups screens
        composable(Screen.ContactGroups.route) {
            ContactGroupsScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onSetFab = onSetFab,
                onNavigateToGroupDetail = { groupId ->
                    navController.navigate(Screen.ContactGroupDetail.createRoute(groupId))
                }
            )
        }

        composable(
            route = Screen.ContactGroupDetail.route,
            arguments = listOf(
                navArgument("groupId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val groupId = backStackEntry.arguments?.getString("groupId") ?: ""
            ContactGroupDetailScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onSetFab = onSetFab,
                groupId = groupId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToContactDetail = { contactId ->
                    navController.navigate(Screen.ContactDetail.createRoute(contactId))
                }
            )
        }

        // Stories screen
        composable(Screen.Stories.route) {
            // Check authentication for this screen
            val screenContext = LocalContext.current
            val screenTokenManager = remember { TokenManager(screenContext) }

            if (!screenTokenManager.isLoggedIn()) {
                LaunchedEffect(Unit) {
                    onLogout()
                }
                return@composable
            }

            val storyViewModel: StoryViewModel = hiltViewModel()
            StoriesScreen(
                onNavigateToStoryCreation = {
                    navController.navigate(Screen.StoryCreation.route)
                },
                onNavigateToStoryViewer = { stories, initialIndex ->
                    // Just navigate to viewer, we'll use mock data for now
                    navController.navigate(Screen.StoryViewer.route)
                },
                storyState = storyViewModel.storyState.collectAsState().value,
                onLoadStories = storyViewModel::loadStories,
                onSetTopAppBar = onSetTopAppBar, // Added this line
                onShowSnackbar = onShowSnackbar // Added this line
            )
        }

        // Story creation screen
        composable(Screen.StoryCreation.route) {
            StoryCreationScreen(
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onClose = {
                    navController.popBackStack()
                },
                onPublish = { uri, caption ->
                    // Navigate back after publishing
                    navController.popBackStack()
                }
            )
        }

        // Story viewer screen
        composable(Screen.StoryViewer.route) {
            // Create mock stories for now
            val mockStories = listOf(
                Story(
                    id = "1",
                    authorName = "John Doe",
                    authorAvatarUrl = "https://i.pravatar.cc/150?img=1",
                    authorId = "user1",
                    timePosted = Date(),
                    segments = listOf(
                        StorySegment(
                            id = "1",
                            type = SegmentType.IMAGE,
                            mediaUrl = "https://picsum.photos/seed/1/300/500",
                            overlayItems = listOf(
                                OverlayItem(
                                    id = "1",
                                    type = OverlayType.TEXT,
                                    content = "Hello World",
                                    position = Offset(0.5f, 0.5f)
                                )
                            )
                        ),
                        StorySegment(
                            id = "2",
                            type = SegmentType.IMAGE,
                            mediaUrl = "https://picsum.photos/seed/2/300/500"
                        )
                    )
                )
            )

            StoryViewerScreen(
                stories = mockStories,
                initialStoryIndex = 0,
                onClose = {
                    navController.popBackStack()
                },
                onReplyToStory = { story, reply ->
                    // Handle reply
                },
                onReactionSelected = { story, reaction ->
                    // Handle reaction
                }
            )
        }

        // Calls screen
        composable(Screen.Calls.route) {
            // Check authentication for this screen
            val screenContext = LocalContext.current
            val screenTokenManager = remember { TokenManager(screenContext) }

            if (!screenTokenManager.isLoggedIn()) {
                LaunchedEffect(Unit) {
                    onLogout()
                }
                return@composable
            }

            val mockCalls = listOf(
                Call(
                    id = "1",
                    contactName = "Alice Smith",
                    callType = CallType.INCOMING,
                    timestamp = Date(),
                    duration = "0:45"
                ),
                Call(
                    id = "2",
                    contactName = "Bob Johnson",
                    callType = CallType.OUTGOING,
                    timestamp = Date(),
                    duration = "1:20"
                ),
                Call(
                    id = "3",
                    contactName = "Charlie Brown",
                    callType = CallType.MISSED,
                    timestamp = Date(),
                    duration = "0:00"
                )
            )
            CallListScreen(
                onSetFab = onSetFab,
                calls = mockCalls
            )
        }

        // Profile screen
        composable(Screen.Profile.route) {
            // Check authentication for this screen
            val screenContext = LocalContext.current
            val screenTokenManager = remember { TokenManager(screenContext) }

            if (!screenTokenManager.isLoggedIn()) {
                LaunchedEffect(Unit) {
                    onLogout()
                }
                return@composable
            }

            val profileViewModel: ProfileViewModel = hiltViewModel()
            ProfileScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onLogout = onLogout,
                onNavigateToSecuritySettings = {
                    navController.navigate(Screen.SecuritySettings.route)
                },
                onNavigateToEditProfile = {
                    navController.navigate(Screen.EditProfile.route)
                },
                onNavigateToNotificationsSettings = {
                    navController.navigate(Screen.NotificationsSettings.route)
                },
                onNavigateToAppearanceSettings = {
                    navController.navigate(Screen.AppearanceSettings.route)
                },
                onNavigateToLanguageSettings = {
                    navController.navigate(Screen.LanguageSettings.route)
                },
                profileState = profileViewModel.profileState.collectAsState().value,
                onClearProfileError = profileViewModel::clearError,
                onPerformLogout = profileViewModel::logout,
                onGetMeenaId = profileViewModel::getMeenaId
            )
        }

        // Edit profile screen
        composable(Screen.EditProfile.route) {
            val profileViewModel: ProfileViewModel = hiltViewModel()
            EditProfileScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onNavigateBack = {
                    navController.popBackStack()
                },
                profileState = profileViewModel.profileState.collectAsState().value,
                onClearProfileError = profileViewModel::clearError,
                onUpdateProfile = profileViewModel::updateProfile,
                onGetMeenaId = profileViewModel::getMeenaId
            )
        }

        // Security settings screen
        composable(Screen.SecuritySettings.route) {
            SecuritySettingsScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToRecoveryPhrase = {
                    navController.navigate(Screen.RecoveryPhrase.route)
                },
                onNavigateToRecoveryPin = {
                    navController.navigate(Screen.RecoveryPin.route)
                },
                onNavigateToRemoteWipePin = {
                    navController.navigate(Screen.RemoteWipePin.route)
                },
                onNavigateToChangePassword = {
                    // TODO: Implement change password screen
                }
            )
        }

        // Appearance settings screen
        composable(Screen.AppearanceSettings.route) {
            val settingsViewModel: SettingsViewModel = hiltViewModel()
            val settingsState by settingsViewModel.settingsState.collectAsState()
            
            AppearanceSettingsScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onNavigateBack = {
                    navController.popBackStack()
                },
                currentTheme = settingsState.theme,
                onUpdateTheme = settingsViewModel::updateTheme,
                error = settingsState.error,
                onClearError = settingsViewModel::clearError
            )
        }

        // Notifications settings screen
        composable(Screen.NotificationsSettings.route) {
            val settingsViewModel: SettingsViewModel = hiltViewModel()
            val settingsState by settingsViewModel.settingsState.collectAsState()

            NotificationsSettingsScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onNavigateBack = {
                    navController.popBackStack()
                },
                notificationsEnabled = settingsState.notificationsEnabled,
                messagePreviewsEnabled = settingsState.messagePreviewsEnabled,
                readReceiptsEnabled = settingsState.readReceiptsEnabled,
                typingIndicatorsEnabled = settingsState.typingIndicatorsEnabled,
                onUpdateNotifications = settingsViewModel::updateNotificationsEnabled,
                onUpdateMessagePreviews = settingsViewModel::updateMessagePreviewsEnabled,
                onUpdateReadReceipts = settingsViewModel::updateReadReceiptsEnabled,
                onUpdateTypingIndicators = settingsViewModel::updateTypingIndicatorsEnabled,
                error = settingsState.error,
                onClearError = settingsViewModel::clearError
            )
        }

        // Language settings screen
        composable(Screen.LanguageSettings.route) {
            val settingsViewModel: SettingsViewModel = hiltViewModel()
            val settingsState by settingsViewModel.settingsState.collectAsState()

            LanguageSettingsScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onNavigateBack = {
                    navController.popBackStack()
                },
                currentLanguage = settingsState.language,
                onUpdateLanguage = settingsViewModel::updateLanguage,
                error = settingsState.error,
                onClearError = settingsViewModel::clearError
            )
        }

        // Group screens
        composable(Screen.CreateGroup.route) {
            val chatViewModel: ChatViewModel = hiltViewModel()
            val uiState by chatViewModel.uiState.collectAsState()
            
            GroupCreationScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToChat = { chatId ->
                    // Navigate to the group chat
                    navController.navigate(Screen.Chat.createRoute(chatId, "Group")) {
                        popUpTo(Screen.CreateGroup.route) { inclusive = true }
                    }
                },
                isCreatingGroup = uiState.isCreatingGroup,
                isGroupCreated = uiState.isGroupCreated,
                newGroupChatId = uiState.newGroupChatId,
                error = uiState.error,
                onClearError = chatViewModel::clearError,
                onCreateGroup = { name, description, initialMembers, privacyType ->
                    chatViewModel.createGroupChat(
                        name = name,
                        description = description,
                        initialMembers = initialMembers,
                        privacyType = privacyType
                    )
                },
                onResetOperationStates = chatViewModel::resetAllOperationStates
            )
        }

        composable(
            route = Screen.GroupDetails.route,
            arguments = listOf(
                navArgument("groupId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val chatViewModel: ChatViewModel = hiltViewModel()
            val uiState by chatViewModel.uiState.collectAsState()
            val conversationListItems by chatViewModel.conversationListItems.collectAsState()
            val groupId = backStackEntry.arguments?.getString("groupId") ?: ""
            
            GroupDetailsScreen(
                onSetTopAppBar = onSetTopAppBar,
                onShowSnackbar = onShowSnackbar,
                groupId = groupId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToChat = { chatId ->
                    navController.navigate(Screen.Chat.createRoute(chatId, "Group")) {
                        popUpTo(Screen.GroupDetails.route) { inclusive = true }
                    }
                },
                conversationListItems = conversationListItems,
                isLoading = uiState.properties.isLoading,
                isGroupOperationInProgress = uiState.groupChatState.createGroupOperation.isInProgress ||
                        uiState.groupChatState.updateGroupOperation.isInProgress ||
                        uiState.groupChatState.addParticipantsOperation.isInProgress ||
                        uiState.groupChatState.removeParticipantsOperation.isInProgress ||
                        uiState.groupChatState.updateAdminsOperation.isInProgress ||
                        uiState.groupChatState.leaveGroupOperation.isInProgress,
                isLeaveGroupSuccessful = uiState.groupChatState.leaveGroupOperation.isSuccessful,
                error = uiState.error,
                currentUserId = chatViewModel.getCurrentUserId(),
                onClearError = chatViewModel::clearError,
                onUpdateGroup = chatViewModel::updateGroupChat,
                onAddMembers = chatViewModel::addGroupChatParticipants,
                onRemoveMembers = chatViewModel::removeGroupChatParticipants,
                onUpdateAdmins = chatViewModel::updateGroupChatAdmins,
                onLeaveGroup = chatViewModel::leaveGroupChat,
                onResetOperationStates = chatViewModel::resetAllOperationStates
            )
        }

        // Media preview screen
        composable(
            route = Screen.MediaPreview.route,
            arguments = listOf(
                navArgument("messageId") { type = NavType.StringType },
                navArgument("initialIndex") {
                    type = NavType.IntType
                    defaultValue = 0
                }
            )
        ) { backStackEntry ->
            val messageId = backStackEntry.arguments?.getString("messageId") ?: ""
            val initialIndex = backStackEntry.arguments?.getInt("initialIndex") ?: 0

            // Get view models
            val chatViewModel: ChatViewModel = hiltViewModel()
            val mediaViewModel: MediaViewModel = hiltViewModel()

            // Handle both sent messages and draft attachments
            if (messageId == "draft") {
                // Preview draft attachments
                val selectedAttachments = chatViewModel.uiState.collectAsState().value.selectedAttachments
                if (selectedAttachments.isNotEmpty()) {
                    MediaPreviewScreen(
                        onSetTopAppBar = onSetTopAppBar,
                        attachments = selectedAttachments,
                        initialAttachmentIndex = initialIndex,
                        onNavigateBack = {
                            navController.popBackStack()
                        },
                        mediaAttachmentHelper = mediaViewModel.mediaAttachmentHelper,
                        onDownloadAttachment = mediaViewModel::downloadMediaAttachment,
                        downloadProgress = selectedAttachments.getOrNull(initialIndex)?.id?.let { attachmentId ->
                            mediaViewModel.getDownloadProgress(attachmentId)
                        }
                    )
                } else {
                    LaunchedEffect(Unit) {
                        navController.popBackStack()
                    }
                }
            } else {
                // Preview sent message attachments
                val message = chatViewModel.getMessageById(messageId)
                if (message?.attachments != null && message.attachments!!.isNotEmpty()) {
                    MediaPreviewScreen(
                        onSetTopAppBar = onSetTopAppBar,
                        attachments = message.attachments!!,
                        initialAttachmentIndex = initialIndex,
                        onNavigateBack = {
                            navController.popBackStack()
                        },
                        mediaAttachmentHelper = mediaViewModel.mediaAttachmentHelper,
                        onDownloadAttachment = mediaViewModel::downloadMediaAttachment,
                        downloadProgress = message.id.let { messageId ->
                            message.attachments?.getOrNull(initialIndex)?.id?.let { attachmentId ->
                                mediaViewModel.getDownloadProgress(attachmentId)
                            }
                        }
                    )
                } else {
                    LaunchedEffect(Unit) {
                        navController.popBackStack()
                    }
                }
            }
        }
    }
}