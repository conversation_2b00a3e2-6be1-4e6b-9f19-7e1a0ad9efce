package com.tfkcolin.meena.ui.contacts

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.components.MeenaTextField
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.components.QRCodeScanner
import com.tfkcolin.meena.ui.contacts.components.RelationshipDropdown

/**
 * Add contact screen.
 * Allows users to add contacts by Meena ID or by scanning a QR code.
 *
 * @param onNavigateBack Navigate back to the contact list screen.
 * @param contactsState The contacts UI state.
 * @param onClearContactsError Callback to clear contact-related errors.
 * @param onResetContactOperationStates Callback to reset all contact operation states.
 * @param onAddContact Callback to add a contact (displayName, notes).
 * @param onUpdateAddContactMeenaId Callback to update the Meena ID in the ViewModel.
 */
@Composable
fun AddContactScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onShowSnackbar: (String) -> Unit,
    onNavigateBack: () -> Unit,
    contactsState: ContactsState,
    onClearContactsError: () -> Unit,
    onResetContactOperationStates: () -> Unit,
    onAddContact: (String?, String?) -> Unit,
    onUpdateAddContactMeenaId: (String) -> Unit
) {
    val snackbarHostState = remember { SnackbarHostState() }
    val focusManager = LocalFocusManager.current

    var userHandle by remember { mutableStateOf("") }
    var displayName by remember { mutableStateOf("") }
    var notes by remember { mutableStateOf("") }
    var relationship by remember { mutableStateOf("friend") } // Default to "friend"
    var isRelationshipMenuExpanded by remember { mutableStateOf(false) }

    // QR code scanner state
    var showScanner by remember { mutableStateOf(false) }

    val relationships = listOf(
        "friend" to stringResource(R.string.relationship_friend),
        "family" to stringResource(R.string.relationship_family),
        "colleague" to stringResource(R.string.relationship_colleague),
        "acquaintance" to stringResource(R.string.relationship_acquaintance)
    )

    /**
     * Add a contact.
     */
    fun addContact() {
        onAddContact(
            if (displayName.isBlank()) null else displayName,
            if (notes.isBlank()) null else notes
        )
    }

    val contactAddedSuccessfully = stringResource(R.string.contact_added_successfully)

    // Check if contact was added successfully
    LaunchedEffect(contactsState.addContactOperation.isSuccessful) {
        if (contactsState.addContactOperation.isSuccessful) {
            onShowSnackbar(contactAddedSuccessfully)
            onResetContactOperationStates()
            onNavigateBack()
        }
    }

    // Show error message
    LaunchedEffect(contactsState.error) {
        contactsState.error?.let {
            onShowSnackbar(it)
            onClearContactsError()
        }
    }

    // Show operation error message
    LaunchedEffect(contactsState.addContactOperation.error) {
        contactsState.addContactOperation.error?.let {
            onShowSnackbar(it)
            onResetContactOperationStates()
        }
    }

    DisposableEffect(Unit) {
        onSetTopAppBar {
            MeenaTopBar(
                title = stringResource(R.string.add_contact_screen_title),
                onBackClick = onNavigateBack,
                actions = {
                    IconButton(onClick = { showScanner = true }) {
                        Icon(
                            imageVector = Icons.Default.QrCodeScanner,
                            contentDescription = stringResource(R.string.scan_qr_code_content_description)
                        )
                    }
                }
            )
        }
        onDispose {
            onSetTopAppBar(null)
        }
    }

    if (showScanner) {
        QRCodeScanner(
            onQRCodeScanned = { scannedMeenaId ->
                userHandle = scannedMeenaId
                showScanner = false
                // Notify viewModel of updated handle and auto-add contact
                onUpdateAddContactMeenaId(scannedMeenaId)
                addContact()
            },
            onClose = { showScanner = false }
        )
    } else {
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Top
            ) {
                Text(
                    text = stringResource(R.string.add_new_contact_headline),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = stringResource(R.string.add_contact_instruction),
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(32.dp))

                OutlinedTextField(
                    value = userHandle,
                    onValueChange = {
                        userHandle = it
                        onUpdateAddContactMeenaId(it)
                    },
                    label = { Text(stringResource(R.string.meena_id_label)) },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.QrCode,
                            contentDescription = null // Decorative icon
                        )
                    },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    ),
                    keyboardActions = KeyboardActions(
                        onNext = { focusManager.moveFocus(FocusDirection.Down) }
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                MeenaTextField(
                    value = displayName,
                    onValueChange = { displayName = it },
                    label = stringResource(R.string.display_name_optional_label),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    ),
                    keyboardActions = KeyboardActions(
                        onNext = { focusManager.moveFocus(FocusDirection.Down) }
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Relationship dropdown
                RelationshipDropdown(
                    selectedRelationship = relationship,
                    onRelationshipSelected = { relationship = it },
                    relationships = relationships,
                    isExpanded = isRelationshipMenuExpanded,
                    onExpandedChange = { isRelationshipMenuExpanded = it },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Notes field
                MeenaTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    label = stringResource(R.string.notes_optional_label),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        capitalization = KeyboardCapitalization.Sentences,
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                            if (userHandle.isNotBlank()) {
                                addContact()
                            }
                        }
                    ),
                    singleLine = false,
                    maxLines = 5
                )

                Spacer(modifier = Modifier.height(32.dp))

                MeenaPrimaryButton(
                    text = stringResource(R.string.add_contact_action_button),
                    onClick = { addContact() },
                    enabled = !contactsState.properties.isLoading &&
                            !contactsState.addContactOperation.isInProgress &&
                            userHandle.isNotBlank(),
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = stringResource(R.string.or_scan_qr_code_prompt),
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(8.dp))

                MeenaPrimaryButton(
                    text = stringResource(R.string.scan_qr_code_button),
                    onClick = { showScanner = true },
                    enabled = !contactsState.properties.isLoading &&
                            !contactsState.addContactOperation.isInProgress,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // TODO: Remove this test button when backend integration is complete
                MeenaPrimaryButton(
                    text = "Fill with test data",
                    onClick = {
                        userHandle = "testuser123"
                        displayName = "Test User"
                        notes = "Test contact for development purposes"
                        relationship = "friend"
                    },
                    enabled = !contactsState.properties.isLoading &&
                            !contactsState.addContactOperation.isInProgress,
                    modifier = Modifier.fillMaxWidth()
                )

                // Add some space at the bottom for scrolling
                Spacer(modifier = Modifier.height(32.dp))
            }

            if (contactsState.properties.isLoading || contactsState.addContactOperation.isInProgress) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}
