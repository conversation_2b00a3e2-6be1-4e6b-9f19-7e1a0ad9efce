package com.tfkcolin.meena.ui.auth

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.FabPosition
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.components.MeenaTextField
import com.tfkcolin.meena.ui.theme.MeenaTheme
import androidx.compose.runtime.* // Import for LaunchedEffect, remember, Animatable
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.res.stringResource
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.CircularProgressIndicator
import com.tfkcolin.meena.ui.components.MeenaTopBar

/**
 * Screen for registering a new user.
 * This screen only asks for a display name.
 *
 * @param onNavigateBack Navigate back to the previous screen.
 * @param displayName The current value of the display name input.
 * @param isLoading Indicates if a registration operation is in progress.
 * @param errorMessage An error message to display, if any.
 * @param onDisplayNameChange Callback for when the display name changes.
 * @param onRegisterClick Callback to initiate the registration process.
 * @param onClearError Callback to clear the current error message.
 */
@Composable
fun RegistrationScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetBottomAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetFab: (@Composable (() -> Unit)?, FabPosition) -> Unit,
    onNavigateBack: () -> Unit,
    displayName: String,
    isLoading: Boolean,
    errorMessage: String?,
    onDisplayNameChange: (String) -> Unit,
    onRegisterClick: (String) -> Unit,
) {
    val focusManager = LocalFocusManager.current
    // Set the TopAppBar for this screen
    DisposableEffect(Unit) {
        onSetTopAppBar {
            MeenaTopBar(
                title = stringResource(R.string.create_your_account_title),
                onBackClick = onNavigateBack
            )
        }
        onSetBottomAppBar(null)
        onSetFab({ null }, FabPosition.End)
        onDispose {
            // Clear the TopAppBar and FAB when leaving this screen
            onSetTopAppBar(null)
            onSetBottomAppBar(null)
            onSetFab(null, FabPosition.End)
        }
    }

    val animatedAlpha = remember { Animatable(0.1f) }

    LaunchedEffect(Unit) {
        animatedAlpha.animateTo(
            targetValue = 0.3f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 3000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }
    Box(modifier = Modifier.fillMaxSize()) {
        Box(modifier = Modifier.fillMaxSize()) {
            // Background Gradient with animated overlay
            Canvas(modifier = Modifier.matchParentSize()) {
                val gradient = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF673AB7), // Deeper purple-blue
                        Color(0xFF512DA8) // Darker purple-blue
                    )
                )
                drawRect(gradient)

                // Add a subtle animated overlay effect
                clipPath(Path().apply {
                    // This creates a subtle curve from bottom left to bottom right, darkening the bottom
                    moveTo(0f, size.height * 0.8f)
                    cubicTo(
                        size.width * 0.2f, size.height * 0.9f,
                        size.width * 0.8f, size.height * 0.9f,
                        size.width, size.height * 0.8f
                    )
                    lineTo(size.width, size.height)
                    lineTo(0f, size.height)
                    close()
                }) {
                    drawRect(Color.Black.copy(alpha = animatedAlpha.value)) // Subtle dark overlay with animation
                }
            }

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 24.dp, vertical = 32.dp), // Adjusted vertical padding
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(R.string.create_your_account_title), // Use string resource
                    style = MaterialTheme.typography.headlineMedium.copy( // Consistent headline style
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimary // Text color for contrast on dark background
                    ),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                Text(
                    text = stringResource(R.string.enter_display_name_description), // Use string resource
                    style = MaterialTheme.typography.bodyLarge.copy(
                        color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.8f) // Softer text color
                    ),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 32.dp)
                )

                // Display name input field
                MeenaTextField(
                    value = displayName,
                    onValueChange = onDisplayNameChange,
                    label = stringResource(R.string.display_name_label), // Use string resource
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                            if (displayName.isNotEmpty()) {
                                onRegisterClick(displayName)
                            }
                        }
                    ),
                    singleLine = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 32.dp)
                )

                // Error message
                if (errorMessage != null) {
                    Text(
                        text = errorMessage, // error message itself is a dynamic value, not a static string
                        color = MaterialTheme.colorScheme.error, // Keep error color for warning
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center, // Center align error message for consistency
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                }

                // Continue button
                MeenaPrimaryButton(
                    text = stringResource(R.string.continue_button), // Use string resource
                    onClick = {
                        focusManager.clearFocus()
                        onRegisterClick(displayName)
                    },
                    enabled = displayName.isNotEmpty() && !isLoading,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
        if (isLoading) {
            CircularProgressIndicator(
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

@Preview()
@Composable
fun RegistrationScreenPreview() {
    MeenaTheme {
        RegistrationScreen(
            onNavigateBack = {},
            displayName = "",
            isLoading = false,
            errorMessage = null,
            onDisplayNameChange = { _ -> },
            onRegisterClick = { _ -> },
            onSetTopAppBar = { _ -> },
            onSetBottomAppBar = { _ -> },
            onSetFab = { _, _ -> },
        )
    }
}