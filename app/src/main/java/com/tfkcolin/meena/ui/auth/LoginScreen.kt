package com.tfkcolin.meena.ui.auth

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.FabPosition
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.components.MeenaPasswordTextField
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.components.MeenaTextField
import com.tfkcolin.meena.ui.components.MeenaTextButton
import com.tfkcolin.meena.ui.theme.MeenaTheme
import androidx.compose.runtime.* // Import for LaunchedEffect, remember, Animatable
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.res.stringResource
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar

/**
 * Login screen.
 *
 * @param onNavigateBack Navigate back to the previous screen.
 * @param onNavigateToRegister Navigate to the registration screen.
 * @param onNavigateToRecovery Navigate to the account recovery screen.
 * @param identifier The current value of the identifier input.
 * @param password The current value of the password input.
 * @param isLoading Indicates if a login operation is in progress.
 * @param errorMessage An error message to display, if any.
 * @param onIdentifierChange Callback for when the identifier changes.
 * @param onPasswordChange Callback for when the password changes.
 * @param onLoginClick Callback to initiate the login process.
 * @param onClearError Callback to clear the current error message.
 */
@Composable
fun LoginScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetBottomAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetFab: (@Composable (() -> Unit)?, FabPosition) -> Unit,
    onShowSnackbar: (String) -> Unit,
    onNavigateBack: () -> Unit,
    onNavigateToRegister: () -> Unit,
    onNavigateToRecovery: () -> Unit,
    identifier: String,
    password: String,
    isLoading: Boolean,
    onIdentifierChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onLoginClick: (String, String) -> Unit,
) {
    val focusManager = LocalFocusManager.current
    DisposableEffect(Unit) {
        onSetTopAppBar {
            MeenaTopBar(
                title = stringResource(R.string.login_title),
                onBackClick = onNavigateBack
            )
        }
        onSetBottomAppBar(null)
        onSetFab({ null }, FabPosition.End)
        onDispose {
            // Clear the TopAppBar and FAB when leaving this screen
            onSetTopAppBar(null)
            onSetBottomAppBar(null)
            onSetFab(null, FabPosition.End)
        }
    }

    val animatedAlpha = remember { Animatable(0.1f) }

    LaunchedEffect(Unit) {
        animatedAlpha.animateTo(
            targetValue = 0.3f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 3000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Background Gradient with animated overlay
        Canvas(modifier = Modifier.matchParentSize()) {
            val gradient = Brush.verticalGradient(
                colors = listOf(
                    Color(0xFF673AB7), // Deeper purple-blue
                    Color(0xFF512DA8) // Darker purple-blue
                )
            )
            drawRect(gradient)

            // Add a subtle animated overlay effect
            clipPath(Path().apply {
                // This creates a subtle curve from bottom left to bottom right, darkening the bottom
                moveTo(0f, size.height * 0.8f)
                cubicTo(
                    size.width * 0.2f, size.height * 0.9f,
                    size.width * 0.8f, size.height * 0.9f,
                    size.width, size.height * 0.8f
                )
                lineTo(size.width, size.height)
                lineTo(0f, size.height)
                close()
            }) {
                drawRect(Color.Black.copy(alpha = animatedAlpha.value)) // Subtle dark overlay with animation
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp, vertical = 32.dp), // Adjusted vertical padding
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(R.string.welcome_to_meena),
                style = MaterialTheme.typography.headlineMedium.copy( // Consistent headline style
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary // Text color for contrast on dark background
                ),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(R.string.sign_in_to_your_account),
                style = MaterialTheme.typography.bodyLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.8f) // Softer text color
                ),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(32.dp))

            MeenaTextField(
                value = identifier,
                onValueChange = onIdentifierChange,
                label = stringResource(R.string.meena_id_label),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) }
                ),
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            MeenaPasswordTextField(
                value = password,
                onValueChange = onPasswordChange,
                label = stringResource(R.string.password_label),
                tint = Color.White,
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Password,
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        if (identifier.isNotBlank() && password.isNotBlank()) {
                            onLoginClick(identifier, password)
                        }
                    }
                ),
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(8.dp))

            MeenaTextButton(
                text = stringResource(R.string.forgot_password_button),
                onClick = onNavigateToRecovery,
                modifier = Modifier.align(Alignment.End)
            )

            Spacer(modifier = Modifier.height(24.dp))

            MeenaPrimaryButton(
                text = stringResource(R.string.sign_in_button),
                onClick = { onLoginClick(identifier, password) },
                enabled = !isLoading && identifier.isNotBlank() && password.isNotBlank(),
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(R.string.dont_have_account_question),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f) // Consistent text color
                )
            )

            MeenaTextButton(
                text = stringResource(R.string.create_account_button),
                onClick = onNavigateToRegister
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun LoginScreenPreview() {
    MeenaTheme {
        LoginScreen(
            onNavigateBack = {},
            onNavigateToRegister = {},
            onNavigateToRecovery = {},
            identifier = "",
            password = "",
            isLoading = false,
            onIdentifierChange = {},
            onPasswordChange = {},
            onLoginClick = { _, _ -> },
            onSetTopAppBar = { _ -> },
            onSetBottomAppBar = { _ -> },
            onSetFab = { _, _ -> },
            onShowSnackbar = {}
        )
    }
}