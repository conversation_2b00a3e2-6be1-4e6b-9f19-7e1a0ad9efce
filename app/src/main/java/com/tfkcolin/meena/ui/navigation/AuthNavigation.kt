package com.tfkcolin.meena.ui.navigation

import androidx.compose.material3.FabPosition
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import androidx.compose.runtime.collectAsState
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.ui.auth.AccountRecoveryScreen
import com.tfkcolin.meena.ui.auth.AuthViewModel
import com.tfkcolin.meena.ui.auth.LoginScreen
import com.tfkcolin.meena.ui.auth.RegistrationScreen
import com.tfkcolin.meena.ui.auth.SecretPhraseScreen
import com.tfkcolin.meena.ui.auth.SetupPinScreen
import com.tfkcolin.meena.ui.auth.TwoFactorAuthScreen
import com.tfkcolin.meena.ui.auth.UniqueIdScreen
import com.tfkcolin.meena.ui.auth.WelcomeScreen
import com.tfkcolin.meena.utils.TokenManager

/**
 * Authentication navigation graph.
 * This handles all authentication-related navigation.
 */
fun NavGraphBuilder.authNavigation(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetBottomAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetFab: (@Composable (() -> Unit)?, FabPosition) -> Unit,
    onShowSnackbar: (String) -> Unit,
    navController: NavHostController,
    onAuthenticationComplete: () -> Unit
) {
    navigation(
        startDestination = Screen.WelcomeScreen.route,
        route = AUTH_GRAPH_ROUTE
    ) {
        // Welcome screen (entry point)
        composable(Screen.WelcomeScreen.route) {
            // Check if the user has tokens but hasn't completed registration
            val context = LocalContext.current
            val tokenManager = remember { TokenManager(context) }

            // If the user has tokens but hasn't completed registration,
            // navigate directly to the UniqueIdScreen
            LaunchedEffect(Unit) {
                if (tokenManager.getAccessToken() != null && !tokenManager.isRegistrationComplete()) {
                    println("User has tokens but hasn't completed registration, navigating to UniqueIdScreen")
                    navController.navigate(Screen.UniqueIdScreen.route) {
                        popUpTo(Screen.WelcomeScreen.route) { inclusive = true }
                    }
                }
            }

            WelcomeScreen(
                onCreateAccount = {
                    navController.navigate(Screen.RegistrationScreen.route)
                },
                onLogin = {
                    navController.navigate(Screen.Login.route)
                }
            )
        }

        // Registration flow
        composable(Screen.RegistrationScreen.route) {
            val viewModel: AuthViewModel = hiltViewModel()
            val authState by viewModel.authState.collectAsState()
            var displayName by remember { mutableStateOf("") }

            // Handle registration state changes
            LaunchedEffect(authState.registerOperation.isSuccessful) {
                if (authState.registerOperation.isSuccessful) {
                    navController.navigate(Screen.UniqueIdScreen.route)
                    viewModel.resetOperationStates()
                }
            }

            // Show error message
            LaunchedEffect(authState.error) {
                authState.error?.let {
                    onShowSnackbar(it)
                    viewModel.clearError()
                }
            }

            RegistrationScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                displayName = displayName,
                isLoading = authState.isLoading,
                onDisplayNameChange = { newDisplayName ->
                    displayName = newDisplayName
                },
                onRegisterClick = { name ->
                    viewModel.anonymousSignIn(name)
                },
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onSetFab = onSetFab,
                errorMessage = authState.error,
            )
        }

        composable(Screen.UniqueIdScreen.route) {
            val context = LocalContext.current
            val tokenManager = remember { TokenManager(context) }
            val userHandle = remember { tokenManager.getUserHandle() }
            var confirmationChecked by remember { mutableStateOf(false) }

            LaunchedEffect(Unit) {
                println("UniqueIdScreen launched with userHandle=$userHandle")
            }

            UniqueIdScreen(
                userHandle = userHandle,
                confirmationChecked = confirmationChecked,
                onConfirmationChange = { confirmationChecked = it },
                onNavigateBack = {
                    navController.popBackStack()
                },
                onContinue = {
                    navController.navigate(Screen.SecretPhraseScreen.route)
                },
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onSetFab = onSetFab ,
            )
        }

        composable(Screen.SecretPhraseScreen.route) {
            val viewModel: AuthViewModel = hiltViewModel()
            val recoveryPhrase by viewModel.recoveryPhrase.collectAsState()
            var confirmationChecked by remember { mutableStateOf(false) }

            // Log the recovery phrase sources for debugging
            LaunchedEffect(Unit) {
                println("SecretPhraseScreen launched with recoveryPhrase from ViewModel=$recoveryPhrase")
            }

            SecretPhraseScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                recoveryPhrase = recoveryPhrase,
                confirmationChecked = confirmationChecked,
                onConfirmationChange = { confirmationChecked = it },
                onContinueClick = {
                    // Mark registration as complete when the user confirms they've saved the recovery phrase
                    viewModel.completeRegistration()
                    navController.navigate(Screen.SetupPinScreen.route)
                },
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onSetFab = onSetFab ,
            )
        }

        composable(Screen.SetupPinScreen.route) {
            val viewModel: AuthViewModel = hiltViewModel()
            val authState by viewModel.authState.collectAsState()
            var pin by remember { mutableStateOf("") }
            var confirmPin by remember { mutableStateOf("") }

            // Handle error messages from ViewModel
            LaunchedEffect(authState.error) {
                authState.error?.let {
                    onShowSnackbar(it)
                    viewModel.clearError()
                }
            }

            SetupPinScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                pin = pin,
                confirmPin = confirmPin,
                isLoading = authState.isLoading,
                onPinChange = { newPin ->
                    pin = newPin
                },
                onConfirmPinChange = { newConfirmPin ->
                    confirmPin = newConfirmPin
                },
                onCompleteRegistration = { pinToSet ->
                    if (pinToSet.length == 6 && pinToSet == confirmPin) {
                        viewModel.completeRegistration()
                        onAuthenticationComplete()
                    } else if (pinToSet.length != 6) {
                        onShowSnackbar("PIN must be 6 digits")
                    } else {
                        onShowSnackbar("PINs do not match")
                    }
                },
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onSetFab = onSetFab,
                errorMessage = authState.error,
            )
        }

        // Login flow
        composable(Screen.Login.route) {
            val viewModel: AuthViewModel = hiltViewModel()
            val authState by viewModel.authState.collectAsState()
            var identifier by remember { mutableStateOf("") }
            var password by remember { mutableStateOf("") }

            // Check if login was successful
            LaunchedEffect(authState.isLoggedIn, authState.requires2fa) {
                if (authState.isLoggedIn) {
                    onAuthenticationComplete()
                    viewModel.resetOperationStates()
                } else if (authState.requires2fa) {
                    navController.navigate(Screen.TwoFactorAuth.route)
                    viewModel.resetOperationStates()
                }
            }

            // Show error message
            LaunchedEffect(authState.error) {
                authState.error?.let {
                    onShowSnackbar(it)
                    viewModel.clearError()
                }
            }

            // Show operation success/error messages
            LaunchedEffect(authState.loginOperation.isSuccessful) {
                if (authState.loginOperation.isSuccessful) {
                    viewModel.resetOperationStates()
                }
            }

            LaunchedEffect(authState.loginOperation.error) {
                authState.loginOperation.error?.let {
                    onShowSnackbar(it)
                    viewModel.resetOperationStates()
                }
            }

            LoginScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToRegister = {
                    navController.navigate(Screen.RegistrationScreen.route) {
                        popUpTo(Screen.Login.route) { inclusive = true }
                    }
                },
                onNavigateToRecovery = {
                    navController.navigate(Screen.AccountRecovery.route)
                },
                identifier = identifier,
                password = password,
                isLoading = authState.isLoading || authState.loginOperation.isInProgress,
                onIdentifierChange = { newIdentifier ->
                    identifier = newIdentifier
                },
                onPasswordChange = { newPassword ->
                    password = newPassword
                },
                onLoginClick = { id, pw ->
                    viewModel.login(id, pw)
                },
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onSetFab = onSetFab ,
                onShowSnackbar = { message ->
                    onShowSnackbar(message)
                }
            )
        }

        // Account recovery
        composable(Screen.AccountRecovery.route) {
            val viewModel: AuthViewModel = hiltViewModel()
            val authState by viewModel.authState.collectAsState()
            var userHandle by remember { mutableStateOf("") }
            var recoveryPhrase by remember { mutableStateOf("") }
            var recoveryPin by remember { mutableStateOf("") }
            var newPassword by remember { mutableStateOf("") }
            var confirmPassword by remember { mutableStateOf("") }
            var passwordError by remember { mutableStateOf<String?>(null) }

            // Check if recovery was successful
            LaunchedEffect(authState.isLoggedIn) {
                if (authState.isLoggedIn) {
                    onAuthenticationComplete()
                    viewModel.resetOperationStates()
                }
            }

            // Show error message
            LaunchedEffect(authState.error) {
                authState.error?.let {
                    onShowSnackbar(it)
                    viewModel.clearError()
                }
            }

            // Show operation success/error messages
            LaunchedEffect(authState.recoverAccountOperation.isSuccessful) {
                if (authState.recoverAccountOperation.isSuccessful) {
                    viewModel.resetOperationStates()
                }
            }

            LaunchedEffect(authState.recoverAccountOperation.error) {
                authState.recoverAccountOperation.error?.let {
                    onShowSnackbar(it)
                    viewModel.resetOperationStates()
                }
            }

            AccountRecoveryScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                userHandle = userHandle,
                recoveryPhrase = recoveryPhrase,
                recoveryPin = recoveryPin,
                newPassword = newPassword,
                confirmPassword = confirmPassword,
                passwordError = passwordError,
                isLoading = authState.isLoading || authState.recoverAccountOperation.isInProgress,
                onUserHandleChange = {
                    userHandle = it
                },
                onRecoveryPhraseChange = {
                    recoveryPhrase = it
                },
                onRecoveryPinChange = {
                    recoveryPin = it
                },
                onNewPasswordChange = {
                    newPassword = it
                    passwordError = if (confirmPassword.isNotEmpty() && it != confirmPassword) {
                        "Passwords do not match"
                    } else {
                        null
                    }
                },
                onConfirmPasswordChange = {
                    confirmPassword = it
                    passwordError = if (it != newPassword) {
                        "Passwords do not match"
                    } else {
                        null
                    }
                },
                onRecoverAccountClick = { uh, rp, rpin, np ->
                    viewModel.recoverAccount(
                        userHandle = uh,
                        recoveryPhrase = rp,
                        recoveryPin = rpin,
                        newPassword = np
                    )
                },
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onSetFab = onSetFab ,
            )
        }

        // Two-factor authentication
        composable(Screen.TwoFactorAuth.route) {
            val viewModel: AuthViewModel = hiltViewModel()
            val authState by viewModel.authState.collectAsState()
            var code by remember { mutableStateOf("") }

            // Check if authentication was successful
            LaunchedEffect(authState.isLoggedIn) {
                if (authState.isLoggedIn) {
                    onAuthenticationComplete()
                }
            }

            LaunchedEffect(authState.error) {
                authState.error?.let {
                    onShowSnackbar(it)
                    viewModel.clearError()
                }
            }

            TwoFactorAuthScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                isLoading = authState.isLoading,
                code = code,
                onCodeChange = { code = it },
                onVerifyCode = { viewModel.twoFactorAuth(it) },
                onSetTopAppBar = onSetTopAppBar,
                onSetBottomAppBar = onSetBottomAppBar,
                onSetFab = onSetFab ,
            )
        }
    }
}

// Route for the auth graph
const val AUTH_GRAPH_ROUTE = "auth_graph"
