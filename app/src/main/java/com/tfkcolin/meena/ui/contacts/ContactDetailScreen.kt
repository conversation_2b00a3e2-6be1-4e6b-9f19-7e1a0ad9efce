package com.tfkcolin.meena.ui.contacts

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Block
import androidx.compose.material.icons.filled.Chat
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.components.MeenaSecondaryButton
import com.tfkcolin.meena.ui.components.MeenaTopBar

/**
 * Contact detail screen.
 *
 * @param contactId The ID of the contact to display.
 * @param onNavigateBack Navigate back to the contact list screen.
 * @param onNavigateToEditContact Navigate to the edit contact screen.
 * @param viewModel The contacts view model.
 */
@Composable
fun ContactDetailScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onShowSnackbar: (String) -> Unit,
    contactId: String,
    onNavigateBack: () -> Unit,
    onNavigateToEditContact: (String) -> Unit,
    viewModel: ContactsViewModel = hiltViewModel()
) {
    val contactsState by viewModel.contactsState.collectAsState()
    val contacts by viewModel.contacts.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Find the contact with the given ID
    val contact = contacts.find { it.id == contactId }

    var showBlockDialog by remember { mutableStateOf(false) }
    var showUnblockDialog by remember { mutableStateOf(false) }

    // Check if contact operations were successful
    LaunchedEffect(
        contactsState.blockContactOperation.isSuccessful,
        contactsState.unblockContactOperation.isSuccessful,
        contactsState.favoriteContactOperation.isSuccessful,
        contactsState.unfavoriteContactOperation.isSuccessful
    ) {
        if (contactsState.blockContactOperation.isSuccessful ||
            contactsState.unblockContactOperation.isSuccessful ||
            contactsState.favoriteContactOperation.isSuccessful ||
            contactsState.unfavoriteContactOperation.isSuccessful
        ) {
            viewModel.resetContactOperationStates()
        }
    }

    // Show error message
    LaunchedEffect(contactsState.error) {
        contactsState.error?.let {
            onShowSnackbar(it)
            viewModel.clearError()
        }
    }

    val contactBlockedSuccessfully = stringResource(R.string.contact_blocked_successfully_message)
    val contactUnblockedSuccessfully = stringResource(R.string.contact_unblocked_successfully_message)
    val contactAddedToFavorites = stringResource(R.string.contact_added_to_favorites_message)
    val contactRemovedFromFavorites = stringResource(R.string.contact_removed_from_favorites_message)

    // Show operation success/error messages
    LaunchedEffect(contactsState.blockContactOperation.isSuccessful) {
        if (contactsState.blockContactOperation.isSuccessful) {
            onShowSnackbar(contactBlockedSuccessfully)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.unblockContactOperation.isSuccessful) {
        if (contactsState.unblockContactOperation.isSuccessful) {
            onShowSnackbar(contactUnblockedSuccessfully)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.favoriteContactOperation.isSuccessful) {
        if (contactsState.favoriteContactOperation.isSuccessful) {
            onShowSnackbar(contactAddedToFavorites)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.unfavoriteContactOperation.isSuccessful) {
        if (contactsState.unfavoriteContactOperation.isSuccessful) {
            onShowSnackbar(contactRemovedFromFavorites)
            viewModel.resetContactOperationStates()
        }
    }

    // Show operation error messages
    LaunchedEffect(contactsState.blockContactOperation.error) {
        contactsState.blockContactOperation.error?.let {
            onShowSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.unblockContactOperation.error) {
        contactsState.unblockContactOperation.error?.let {
            onShowSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.favoriteContactOperation.error) {
        contactsState.favoriteContactOperation.error?.let {
            onShowSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.unfavoriteContactOperation.error) {
        contactsState.unfavoriteContactOperation.error?.let {
            onShowSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    DisposableEffect(Unit) {
        onSetTopAppBar {
            MeenaTopBar(
                title = stringResource(R.string.contact_details_screen_title),
                onBackClick = onNavigateBack,
                actions = {
                    if (contact != null) {
                        androidx.compose.material3.IconButton(
                            onClick = { onNavigateToEditContact(contactId) }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Edit,
                                contentDescription = stringResource(R.string.edit_contact_content_description)
                            )
                        }
                    }
                }
            )
        }
        onDispose {
            onSetTopAppBar(null)
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        if (contact == null) {
            // Contact not found
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(R.string.contact_not_found_headline),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(16.dp))

                MeenaPrimaryButton(
                    text = stringResource(R.string.go_back_button),
                    onClick = onNavigateBack
                )
            }
        } else {
            // Contact details
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Profile picture or placeholder
                Box(
                    modifier = Modifier
                        .size(120.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)),
                    contentAlignment = Alignment.Center
                ) {
                    val displayName = contact.displayName ?: stringResource(R.string.contact_item_default_user_name)
                    val initial = displayName.firstOrNull()?.uppercase() ?: "U"

                    Text(
                        text = initial,
                        style = MaterialTheme.typography.headlineLarge.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Display name
                Text(
                    text = contact.displayName ?: stringResource(R.string.contact_item_default_user_name),
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    textAlign = TextAlign.Center
                )

                // Blocked indicator
                if (contact.isBlocked()) {
                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Block,
                            contentDescription = stringResource(R.string.blocked_content_description),
                            tint = MaterialTheme.colorScheme.error
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = stringResource(R.string.blocked_status),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))

                // Contact info card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.contact_information_headline),
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold
                            )
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = stringResource(R.string.user_id_label),
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                modifier = Modifier.width(100.dp)
                            )

                            Text(
                                text = contact.contactId,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = stringResource(R.string.relationship_label),
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                modifier = Modifier.width(100.dp)
                            )

                            Text(
                                text = when (contact.relationship) {
                                    "friend" -> stringResource(R.string.relationship_friend)
                                    "family" -> stringResource(R.string.relationship_family)
                                    "colleague" -> stringResource(R.string.relationship_colleague)
                                    "acquaintance" -> stringResource(R.string.relationship_acquaintance)
                                    else -> stringResource(R.string.relationship_none)
                                },
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }

                        if (contact.notes != null) {
                            Spacer(modifier = Modifier.height(8.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Text(
                                    text = stringResource(R.string.notes_label),
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontWeight = FontWeight.Bold
                                    ),
                                    modifier = Modifier.width(100.dp)
                                )

                                Text(
                                    text = contact.notes,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = stringResource(R.string.added_on_label),
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                modifier = Modifier.width(100.dp)
                            )

                            Text(
                                text = contact.createdAt ?: stringResource(R.string.unknown_date),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }

                        if (contact.lastInteractionAt != null) {
                            Spacer(modifier = Modifier.height(8.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Text(
                                    text = stringResource(R.string.last_active_label),
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontWeight = FontWeight.Bold
                                    ),
                                    modifier = Modifier.width(100.dp)
                                )

                                Text(
                                    text = contact.lastInteractionAt,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))

                // Action buttons
                MeenaPrimaryButton(
                    text = stringResource(R.string.message_button),
                    onClick = { /* TODO: Navigate to chat screen */ },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Favorite/Unfavorite button
                if (contact.isFavorite) {
                    MeenaSecondaryButton(
                        text = stringResource(R.string.remove_from_favorites_button),
                        onClick = { viewModel.removeFromFavorites(contact.id) },
                        enabled = !contactsState.properties.isLoading &&
                                !contactsState.unfavoriteContactOperation.isInProgress,
                        modifier = Modifier.fillMaxWidth()
                    )
                } else {
                    MeenaSecondaryButton(
                        text = stringResource(R.string.add_to_favorites_button),
                        onClick = { viewModel.addToFavorites(contact.id) },
                        enabled = !contactsState.properties.isLoading &&
                                !contactsState.favoriteContactOperation.isInProgress,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Block/Unblock button
                if (contact.isBlocked()) {
                    MeenaSecondaryButton(
                        text = stringResource(R.string.unblock_contact_button),
                        onClick = { showUnblockDialog = true },
                        enabled = !contactsState.properties.isLoading &&
                                !contactsState.unblockContactOperation.isInProgress,
                        modifier = Modifier.fillMaxWidth()
                    )
                } else {
                    MeenaSecondaryButton(
                        text = stringResource(R.string.block_contact_button),
                        onClick = { showBlockDialog = true },
                        enabled = !contactsState.properties.isLoading &&
                                !contactsState.blockContactOperation.isInProgress,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }

        if (contactsState.properties.isLoading ||
            contactsState.blockContactOperation.isInProgress ||
            contactsState.unblockContactOperation.isInProgress ||
            contactsState.favoriteContactOperation.isInProgress ||
            contactsState.unfavoriteContactOperation.isInProgress) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }

        // Block confirmation dialog
        if (showBlockDialog) {
            AlertDialog(
                onDismissRequest = { showBlockDialog = false },
                title = { Text(stringResource(R.string.block_contact_dialog_title)) },
                text = { Text(stringResource(R.string.block_contact_dialog_message)) },
                confirmButton = {
                    TextButton(
                        onClick = {
                            showBlockDialog = false
                            contact?.let { viewModel.blockContact(it.id) }
                        }
                    ) {
                        Text(stringResource(R.string.block_button_text))
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = { showBlockDialog = false }
                    ) {
                        Text(stringResource(R.string.cancel_button))
                    }
                }
            )
        }

        // Unblock confirmation dialog
        if (showUnblockDialog) {
            AlertDialog(
                onDismissRequest = { showUnblockDialog = false },
                title = { Text(stringResource(R.string.unblock_contact_dialog_title)) },
                text = { Text(stringResource(R.string.unblock_contact_dialog_message)) },
                confirmButton = {
                    TextButton(
                        onClick = {
                            showUnblockDialog = false
                            contact?.let { viewModel.unblockContact(it.id) }
                        }
                    ) {
                        Text(stringResource(R.string.unblock_button_text))
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = { showUnblockDialog = false }
                    ) {
                        Text(stringResource(R.string.cancel_button))
                    }
                }
            )
        }
    }
}