package com.tfkcolin.meena.ui.auth

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.FabPosition
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.components.MeenaPasswordTextField
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.components.MeenaTextField
import com.tfkcolin.meena.ui.components.MeenaTextButton
import com.tfkcolin.meena.ui.theme.MeenaTheme
import androidx.compose.runtime.* // Import for LaunchedEffect, remember, Animatable
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.res.stringResource
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar

/**
 * Account recovery screen.
 *
 * @param onNavigateBack Navigate back to the previous screen.
 * @param userHandle The current value of the user handle input.
 * @param recoveryPhrase The current value of the recovery phrase input.
 * @param recoveryPin The current value of the recovery PIN input.
 * @param newPassword The current value of the new password input.
 * @param confirmPassword The current value of the confirm password input.
 * @param passwordError An error message for password validation, if any.
 * @param isLoading Indicates if a recovery operation is in progress.
 * @param onUserHandleChange Callback for when the user handle changes.
 * @param onRecoveryPhraseChange Callback for when the recovery phrase changes.
 * @param onRecoveryPinChange Callback for when the recovery PIN changes.
 * @param onNewPasswordChange Callback for when the new password changes.
 * @param onConfirmPasswordChange Callback for when the confirm password changes.
 * @param onRecoverAccountClick Callback to initiate the account recovery process.
 * @param onClearError Callback to clear the current error message.
 */
@Composable
fun AccountRecoveryScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetBottomAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetFab: (@Composable (() -> Unit)?, FabPosition) -> Unit,
    onNavigateBack: () -> Unit,
    userHandle: String,
    recoveryPhrase: String,
    recoveryPin: String,
    newPassword: String,
    confirmPassword: String,
    passwordError: String?,
    isLoading: Boolean,
    onUserHandleChange: (String) -> Unit,
    onRecoveryPhraseChange: (String) -> Unit,
    onRecoveryPinChange: (String) -> Unit,
    onNewPasswordChange: (String) -> Unit,
    onConfirmPasswordChange: (String) -> Unit,
    onRecoverAccountClick: (String, String, String, String) -> Unit,
) {
    val focusManager = LocalFocusManager.current
    val scrollState = rememberScrollState()

    DisposableEffect(Unit) {
        onSetTopAppBar {
            MeenaTopBar(
                title = stringResource(R.string.account_recovery_title),
                onBackClick = onNavigateBack
            )
        }
        onSetBottomAppBar(null)
        onSetFab({ null }, FabPosition.End)
        onDispose {
            // Clear the TopAppBar and FAB when leaving this screen
            onSetTopAppBar(null)
            onSetBottomAppBar(null)
            onSetFab(null, FabPosition.End)
        }
    }

    val animatedAlpha = remember { Animatable(0.1f) }

    LaunchedEffect(Unit) {
        animatedAlpha.animateTo(
            targetValue = 0.3f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 3000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Background Gradient with animated overlay
        Canvas(modifier = Modifier.matchParentSize()) {
            val gradient = Brush.verticalGradient(
                colors = listOf(
                    Color(0xFF673AB7), // Deeper purple-blue
                    Color(0xFF512DA8) // Darker purple-blue
                )
            )
            drawRect(gradient)

            // Add a subtle animated overlay effect
            clipPath(Path().apply {
                // This creates a subtle curve from bottom left to bottom right, darkening the bottom
                moveTo(0f, size.height * 0.8f)
                cubicTo(
                    size.width * 0.2f, size.height * 0.9f,
                    size.width * 0.8f, size.height * 0.9f,
                    size.width, size.height * 0.8f
                )
                lineTo(size.width, size.height)
                lineTo(0f, size.height)
                close()
            }) {
                drawRect(Color.Black.copy(alpha = animatedAlpha.value)) // Subtle dark overlay with animation
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp, vertical = 32.dp) // Adjusted vertical padding
                .verticalScroll(scrollState),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Top
        ) {
            Text(
                text = stringResource(R.string.recover_account_headline), // Extracted
                style = MaterialTheme.typography.headlineMedium.copy( // Consistent headline style
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary // Text color for contrast on dark background
                ),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(R.string.recover_account_description), // Extracted
                style = MaterialTheme.typography.bodyLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.8f) // Softer text color
                ),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(32.dp))

            Text(
                text = stringResource(R.string.meena_id_registration_hint), // Extracted
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f) // Consistent muted color
                )
            )

            Spacer(modifier = Modifier.height(8.dp))

            MeenaTextField(
                value = userHandle,
                onValueChange = onUserHandleChange,
                label = stringResource(R.string.meena_id_label), // Extracted
                placeholder = stringResource(R.string.meena_id_placeholder), // Extracted
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) }
                ),
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(R.string.recovery_phrase_registration_hint), // Extracted
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f) // Consistent muted color
                )
            )

            Spacer(modifier = Modifier.height(8.dp))

            MeenaTextField(
                value = recoveryPhrase,
                onValueChange = onRecoveryPhraseChange,
                label = stringResource(R.string.recovery_phrase_label), // Extracted
                placeholder = stringResource(R.string.recovery_phrase_placeholder), // Extracted
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) }
                ),
                singleLine = false,
                maxLines = 3,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(R.string.recovery_pin_registration_hint), // Extracted
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f) // Consistent muted color
                )
            )

            Spacer(modifier = Modifier.height(8.dp))

            MeenaPasswordTextField(
                tint = Color.White,
                value = recoveryPin,
                onValueChange = {
                    // Only allow digits and limit to 6 characters
                    if (it.length <= 6 && it.all { char -> char.isDigit() }) {
                        onRecoveryPinChange(it)
                    }
                },
                label = stringResource(R.string.recovery_pin_label), // Extracted
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.NumberPassword,
                    imeAction = ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) }
                ),
                isError = recoveryPin.isNotEmpty() && recoveryPin.length != 6,
                errorText = if (recoveryPin.isNotEmpty() && recoveryPin.length != 6)
                    stringResource(R.string.pin_length_error) else null, // Extracted
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            MeenaPasswordTextField(
                tint = Color.White,
                value = newPassword,
                onValueChange = onNewPasswordChange,
                label = stringResource(R.string.new_password_label), // Extracted
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Password,
                    imeAction = ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) }
                ),
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            MeenaPasswordTextField(
                tint = Color.White,
                value = confirmPassword,
                onValueChange = onConfirmPasswordChange,
                label = stringResource(R.string.confirm_new_password_label), // Extracted
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Password,
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        if (userHandle.isNotBlank() && recoveryPhrase.isNotBlank() &&
                            recoveryPin.length == 6 && recoveryPin.all { it.isDigit() } &&
                            newPassword.length >= 8 && newPassword == confirmPassword
                        ) {
                            onRecoverAccountClick(
                                userHandle,
                                recoveryPhrase,
                                recoveryPin,
                                newPassword
                            )
                        }
                    }
                ),
                isError = passwordError != null,
                errorText = passwordError,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(32.dp))

            MeenaPrimaryButton(
                text = stringResource(R.string.recover_account_button), // Extracted
                onClick = {
                    if (userHandle.isNotBlank() && recoveryPhrase.isNotBlank() &&
                        recoveryPin.length == 6 && recoveryPin.all { it.isDigit() } &&
                        newPassword.length >= 8 && newPassword == confirmPassword
                    ) {
                        onRecoverAccountClick(userHandle, recoveryPhrase, recoveryPin, newPassword)
                    }
                },
                enabled = !isLoading &&
                        userHandle.isNotBlank() &&
                        recoveryPhrase.isNotBlank() &&
                        recoveryPin.length == 6 && recoveryPin.all { it.isDigit() } &&
                        newPassword.length >= 8 && newPassword == confirmPassword,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            MeenaTextButton(
                text = stringResource(R.string.back_to_sign_in_button), // Extracted
                onClick = onNavigateBack
            )

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AccountRecoveryScreenPreview() {
    MeenaTheme {
        AccountRecoveryScreen(
            onNavigateBack = {},
            userHandle = "MEENA1234",
            recoveryPhrase = "apple banana cherry date elder fig grape honeydew kiwi",
            recoveryPin = "123456",
            newPassword = "NewSecurePassword123",
            confirmPassword = "NewSecurePassword123",
            passwordError = null,
            isLoading = false,
            onUserHandleChange = {},
            onRecoveryPhraseChange = {},
            onRecoveryPinChange = {},
            onNewPasswordChange = {},
            onConfirmPasswordChange = {},
            onRecoverAccountClick = { _, _, _, _ -> },
            onSetTopAppBar = { _ -> },
            onSetBottomAppBar = { _ -> },
            onSetFab = { _, _ -> }
        )
    }
}