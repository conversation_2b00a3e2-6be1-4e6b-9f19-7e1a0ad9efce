package com.tfkcolin.meena.ui.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.FabPosition
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.tfkcolin.meena.ui.chat.ChatViewModel
import com.tfkcolin.meena.ui.contacts.ContactsViewModel
import com.tfkcolin.meena.ui.main.mainNavigation // New import
import com.tfkcolin.meena.ui.viewmodels.MainViewModel
import com.tfkcolin.meena.ui.viewmodels.SnackbarEvent
import com.tfkcolin.meena.utils.TokenManager

/**
 * Main navigation graph for the app.
 *
 * @param navController The navigation controller.
 * @param startDestination The start destination.
 */
@Composable
fun NavGraph(
    navController: NavHostController,
    startDestination: String
) {
    val context = LocalContext.current
    val tokenManager = remember { TokenManager(context) }

    // Check if user is logged in, if not, navigate to auth flow
    LaunchedEffect(Unit) {
        if (!tokenManager.isLoggedIn()) {
            // User is not logged in, navigate to auth flow
            navController.navigate(AUTH_GRAPH_ROUTE) {
                popUpTo(MAIN_GRAPH_ROUTE) { inclusive = true } // Changed to MAIN_GRAPH_ROUTE
            }
        }
    }

    val contactsViewModel: ContactsViewModel = hiltViewModel()
    val chatViewModel: ChatViewModel = hiltViewModel()
    val mainViewModel: MainViewModel = hiltViewModel()

    // Observe contacts and update chat view model
    val uiContacts by contactsViewModel.uiContacts.collectAsStateWithLifecycle()
    LaunchedEffect(uiContacts) {
        val contactNameMap = uiContacts.associate { it.contactUserId to it.name }
        val contactAvatarMap = uiContacts.associate { it.contactUserId to it.avatarUrl }
        chatViewModel.updateContactNameMap(contactNameMap)
        chatViewModel.updateContactAvatarMap(contactAvatarMap)
    }

    val snackbarHostState = remember { SnackbarHostState() }
    // State to hold the composable for the TopAppBar and FAB
    var topAppBarContent: @Composable (() -> Unit)? by remember { mutableStateOf(null) }
    var bottomBarContent: @Composable (() -> Unit)? by remember { mutableStateOf(null) }
    var fabContent: @Composable (() -> Unit)? by remember { mutableStateOf(null) }
    var fabPosition: FabPosition by remember { mutableStateOf(FabPosition.End) }

    // Observe snackbar events from the ViewModel
    LaunchedEffect(mainViewModel.snackbarEvents) {
        mainViewModel.snackbarEvents.collect { event ->
            when (event) {
                is SnackbarEvent.ShowMessage -> {
                    snackbarHostState.showSnackbar(
                        message = event.message,
                        actionLabel = event.actionLabel,
                        duration = event.duration
                    )
                }
            }
        }
    }

    Scaffold(
        topBar = {
            topAppBarContent?.invoke()
        },
        floatingActionButton = {
            fabContent?.invoke()
        },
        bottomBar = {
            bottomBarContent?.invoke()
        },
        floatingActionButtonPosition = fabPosition,
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ){
        NavHost(
            modifier = Modifier.padding(it),
            navController = navController,
            startDestination = startDestination
        ) {
            // Auth navigation graph
            authNavigation(
                onSetTopAppBar = { content -> topAppBarContent = content },
                onSetBottomAppBar = { content -> bottomBarContent = content },
                onSetFab = { content, position ->
                    fabContent = content
                    fabPosition = position
                },
                onShowSnackbar = { message ->
                    mainViewModel.showSnackbar(message)
                },
                navController = navController,
                onAuthenticationComplete = {
                    navController.navigate(MAIN_GRAPH_ROUTE) { // Changed to MAIN_GRAPH_ROUTE
                        popUpTo(AUTH_GRAPH_ROUTE) { inclusive = true }
                    }
                }
            )

            // Main navigation graph
            mainNavigation( // New call to mainNavigation
                onSetTopAppBar = { content -> topAppBarContent = content },
                onSetBottomAppBar = { content -> bottomBarContent = content },
                onSetFab = { content, position ->
                    fabContent = content
                    fabPosition = position
                },
                onShowSnackbar = { message ->
                    mainViewModel.showSnackbar(message)
                },
                navController = navController,
                onLogout = {
                    navController.navigate(AUTH_GRAPH_ROUTE) {
                        popUpTo(MAIN_GRAPH_ROUTE) { inclusive = true } // Changed to MAIN_GRAPH_ROUTE
                    }
                },
                contactsViewModel = contactsViewModel,
                chatViewModel = chatViewModel
            )
        }
    }

}

const val MAIN_GRAPH_ROUTE = "main_graph" // New constant
