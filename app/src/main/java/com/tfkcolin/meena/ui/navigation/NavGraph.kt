package com.tfkcolin.meena.ui.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Chat
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Archive
import androidx.compose.material.icons.filled.Call
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.GroupAdd
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Unarchive
import androidx.compose.material3.FabPosition
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.navigation
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.chat.ChatViewModel
import com.tfkcolin.meena.ui.components.BottomNavBar
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.contacts.ContactsViewModel
import com.tfkcolin.meena.ui.main.mainNavigation // New import
import com.tfkcolin.meena.ui.viewmodels.MainViewModel
import com.tfkcolin.meena.ui.viewmodels.SnackbarEvent
import com.tfkcolin.meena.utils.TokenManager

/**
 * Main navigation graph for the app.
 *
 * @param navController The navigation controller.
 * @param startDestination The start destination.
 */
@Composable
fun NavGraph(
    navController: NavHostController,
    startDestination: String
) {
    val context = LocalContext.current
    val tokenManager = remember { TokenManager(context) }

    // Check if user is logged in, if not, navigate to auth flow
    LaunchedEffect(Unit) {
        if (!tokenManager.isLoggedIn()) {
            // User is not logged in, navigate to auth flow
            navController.navigate(AUTH_GRAPH_ROUTE) {
                popUpTo(MAIN_GRAPH_ROUTE) { inclusive = true } // Changed to MAIN_GRAPH_ROUTE
            }
        }
    }

    val contactsViewModel: ContactsViewModel = hiltViewModel()
    val chatViewModel: ChatViewModel = hiltViewModel()
    val mainViewModel: MainViewModel = hiltViewModel()

    // Observe contacts and update chat view model
    val uiContacts by contactsViewModel.uiContacts.collectAsStateWithLifecycle()
    LaunchedEffect(uiContacts) {
        val contactNameMap = uiContacts.associate { it.contactUserId to it.name }
        val contactAvatarMap = uiContacts.associate { it.contactUserId to it.avatarUrl }
        chatViewModel.updateContactNameMap(contactNameMap)
        chatViewModel.updateContactAvatarMap(contactAvatarMap)
    }

    val snackbarHostState = remember { SnackbarHostState() }

    // Get current navigation destination
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    // Get UI configuration based on current route
    val uiConfig = remember(currentRoute) {
        NavigationUiHelper.getUiConfigForCurrentRoute(currentRoute)
    }

    // Observe snackbar events from the ViewModel
    LaunchedEffect(mainViewModel.snackbarEvents) {
        mainViewModel.snackbarEvents.collect { event ->
            when (event) {
                is SnackbarEvent.ShowMessage -> {
                    snackbarHostState.showSnackbar(
                        message = event.message,
                        actionLabel = event.actionLabel,
                        duration = event.duration
                    )
                }
            }
        }
    }

    Scaffold(
        topBar = {
            if (uiConfig.showTopBar) {
                MeenaTopBar(
                    title = uiConfig.topBarTitle,
                    onBackClick = if (uiConfig.showBackButton) {
                        { navController.popBackStack() }
                    } else null,
                    onSearchClick = if (uiConfig.showSearchButton) {
                        {
                            when (currentRoute) {
                                Screen.Chats.route -> navController.navigate(Screen.Search.route)
                                Screen.Chat.route -> {
                                    // Handle chat search - we'll need to get chat details
                                    // This will be handled by the individual screen
                                }
                            }
                        }
                    } else null,
                    actions = {
                        // Handle top bar actions based on current screen
                        when (currentRoute) {
                            Screen.Chats.route -> {
                                val chatViewModel: ChatViewModel = hiltViewModel()
                                val chatUiState by chatViewModel.uiState.collectAsStateWithLifecycle()

                                // Archive/Unarchive button
                                IconButton(
                                    onClick = {
                                        chatViewModel.toggleShowArchivedChats(!chatUiState.showArchivedChats)
                                    }
                                ) {
                                    Icon(
                                        imageVector = if (chatUiState.showArchivedChats)
                                            Icons.Default.Unarchive else Icons.Default.Archive,
                                        contentDescription = if (chatUiState.showArchivedChats)
                                            stringResource(R.string.show_active_chats_content_description)
                                        else stringResource(R.string.show_archived_chats_content_description)
                                    )
                                }

                                // Refresh button
                                IconButton(onClick = { chatViewModel.loadChats() }) {
                                    Icon(
                                        imageVector = Icons.Default.Refresh,
                                        contentDescription = stringResource(R.string.refresh_content_description)
                                    )
                                }
                            }
                        }
                    }
                )
            }
        },
        floatingActionButton = {
            if (uiConfig.showFab) {
                when (currentRoute) {
                    Screen.Chats.route -> {
                        // FAB with menu for chat list
                        var showFabMenu by remember { mutableStateOf(false) }

                        if (showFabMenu) {
                            // Extended FAB menu - this is a simplified version
                            FloatingActionButton(
                                onClick = {
                                    showFabMenu = false
                                    navController.navigate(Screen.NewChat.route)
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.AutoMirrored.Filled.Chat,
                                    contentDescription = stringResource(R.string.new_chat_content_description)
                                )
                            }
                        } else {
                            FloatingActionButton(
                                onClick = { showFabMenu = true }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = stringResource(R.string.open_menu_content_description)
                                )
                            }
                        }
                    }
                    Screen.Contacts.route -> {
                        FloatingActionButton(
                            onClick = { navController.navigate(Screen.AddContact.route) }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "Add Contact"
                            )
                        }
                    }
                    Screen.Calls.route -> {
                        FloatingActionButton(
                            onClick = { /* Handle new call */ }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Call,
                                contentDescription = "New Call"
                            )
                        }
                    }
                    Screen.ContactGroups.route -> {
                        FloatingActionButton(
                            onClick = { /* Handle new contact group */ }
                        ) {
                            Icon(
                                imageVector = Icons.Default.GroupAdd,
                                contentDescription = "New Group"
                            )
                        }
                    }
                    Screen.ContactGroupDetail.route -> {
                        FloatingActionButton(
                            onClick = { /* Handle add member to group */ }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "Add Member"
                            )
                        }
                    }
                }
            }
        },
        bottomBar = {
            if (uiConfig.showBottomBar) {
                BottomNavBar(navController = navController)
            }
        },
        floatingActionButtonPosition = uiConfig.fabPosition,
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ){
        NavHost(
            modifier = Modifier.padding(it),
            navController = navController,
            startDestination = startDestination
        ) {
            // Auth navigation graph
            authNavigation(
                onShowSnackbar = { message ->
                    mainViewModel.showSnackbar(message)
                },
                navController = navController,
                onAuthenticationComplete = {
                    navController.navigate(MAIN_GRAPH_ROUTE) { // Changed to MAIN_GRAPH_ROUTE
                        popUpTo(AUTH_GRAPH_ROUTE) { inclusive = true }
                    }
                }
            )

            // Main navigation graph
            mainNavigation( // New call to mainNavigation
                onShowSnackbar = { message ->
                    mainViewModel.showSnackbar(message)
                },
                navController = navController,
                onLogout = {
                    navController.navigate(AUTH_GRAPH_ROUTE) {
                        popUpTo(MAIN_GRAPH_ROUTE) { inclusive = true } // Changed to MAIN_GRAPH_ROUTE
                    }
                },
                contactsViewModel = contactsViewModel,
                chatViewModel = chatViewModel
            )
        }
    }

}

const val MAIN_GRAPH_ROUTE = "main_graph" // New constant
