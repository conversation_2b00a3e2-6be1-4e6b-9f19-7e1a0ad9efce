package com.tfkcolin.meena.ui.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.LightMode
import androidx.compose.material.icons.filled.SettingsBrightness
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.components.SettingsItem

/**
 * Appearance settings screen.
 * Allows users to customize the app's appearance, such as theme.
 *
 * @param onNavigateBack Navigate back to the profile screen.
 * @param viewModel The settings view model.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppearanceSettingsScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onShowSnackbar: (String) -> Unit,
    onNavigateBack: () -> Unit,
    currentTheme: String,
    onUpdateTheme: (String) -> Unit,
    error: String?,
    onClearError: () -> Unit
) {
    val scrollState = rememberScrollState()

    // Show error message
    LaunchedEffect(error) {
        error?.let {
            onShowSnackbar(it)
            onClearError()
        }
    }

    DisposableEffect(Unit) {
        onSetTopAppBar {
            MeenaTopBar(
                title = stringResource(R.string.appearance),
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                }
            )
        }
        onDispose {
            onSetTopAppBar(null)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        // Theme section
        Text(
            text = stringResource(R.string.theme),
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = stringResource(R.string.choose_how_the_app_appears_on_your_device),
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        // System theme option
        ThemeOption(
            title = stringResource(R.string.system_default),
            description = stringResource(R.string.follow_your_devices_theme_settings),
            icon = Icons.Default.SettingsBrightness,
            isSelected = currentTheme == "system",
            onClick = { onUpdateTheme("system") }
        )

        // Light theme option
        ThemeOption(
            title = stringResource(R.string.light),
            description = stringResource(R.string.always_use_light_theme),
            icon = Icons.Default.LightMode,
            isSelected = currentTheme == "light",
            onClick = { onUpdateTheme("light") }
        )

        // Dark theme option
        ThemeOption(
            title = stringResource(R.string.dark),
            description = stringResource(R.string.always_use_dark_theme),
            icon = Icons.Default.DarkMode,
            isSelected = currentTheme == "dark",
            onClick = { onUpdateTheme("dark") }
        )

        Spacer(modifier = Modifier.height(16.dp))
    }
}

/**
 * Theme option item.
 *
 * @param title The title of the theme option.
 * @param description The description of the theme option.
 * @param icon The icon of the theme option.
 * @param isSelected Whether the theme option is selected.
 * @param onClick The action to perform when the theme option is clicked.
 */
@Composable
private fun ThemeOption(
    title: String,
    description: String,
    icon: ImageVector,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    SettingsItem(
        title = title,
        description = description,
        icon = icon,
        onClick = onClick,
        endContent = {
            RadioButton(
                selected = isSelected,
                onClick = onClick
            )
        }
    )
}
