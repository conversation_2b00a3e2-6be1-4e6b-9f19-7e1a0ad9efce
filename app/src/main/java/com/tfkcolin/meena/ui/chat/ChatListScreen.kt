package com.tfkcolin.meena.ui.chat

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Chat
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Archive
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Group
import androidx.compose.material.icons.filled.GroupAdd
import androidx.compose.material.icons.filled.Public
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Unarchive
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FabPosition
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.data.models.ConversationType
import com.tfkcolin.meena.ui.components.BottomNavBar
import com.tfkcolin.meena.ui.components.ChatOptionsMenu
import com.tfkcolin.meena.ui.components.ConversationTypeTabs
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.components.MuteChatDialog
import com.tfkcolin.meena.ui.components.TelegramChatListItem
import com.tfkcolin.meena.ui.models.ConversationListItem
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Chat list screen.
 *
 * @param onNavigateToChat Navigate to the chat screen.
 * @param onNavigateToNewChat Navigate to the new chat screen.
 */
@Composable
fun ChatListScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetBottomAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetFab: (@Composable (() -> Unit)?, FabPosition) -> Unit,
    onShowSnackbar: (String) -> Unit,
    onNavigateToChat: (String) -> Unit,
    onNavigateToNewChat: () -> Unit,
    onNavigateToSearch: () -> Unit,
    onNavigateToCreateGroup: () -> Unit,
    chatUiState: ChatUiState,
    chats: List<Chat>,
    conversationListItems: List<ConversationListItem>,
    onClearChatError: () -> Unit,
    onArchiveChat: (String, Boolean) -> Unit,
    onMuteChat: (String, Boolean) -> Unit,
    onPinChat: (String, Boolean) -> Unit,
    onToggleShowArchivedChats: (Boolean) -> Unit,
    onLoadChats: () -> Unit,
    getOneToOneConversations: () -> List<ConversationListItem>,
    getGroupConversations: () -> List<ConversationListItem>,
    getChannelConversations: () -> List<ConversationListItem>
) {
    // State for chat options
    var selectedConversation by remember { mutableStateOf<ConversationListItem?>(null) }
    var showChatOptions by remember { mutableStateOf(false) }
    var showMuteDialog by remember { mutableStateOf(false) }
    var showDeleteConfirmation by remember { mutableStateOf(false) }

    // State for conversation type tabs
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    var selectedConversationType by remember { mutableStateOf(ConversationType.ONE_TO_ONE) }

    // Show error message
    LaunchedEffect(chatUiState.error) {
        chatUiState.error?.let {
            onShowSnackbar(it)
            onClearChatError()
        }
    }

    // Chat options menu
    if (showChatOptions && selectedConversation != null) {
        ChatOptionsMenu(
            conversation = selectedConversation!!,
            isExpanded = showChatOptions,
            onDismiss = { showChatOptions = false },
            onArchiveClick = {
                selectedConversation?.let { conversation ->
                    onArchiveChat(conversation.id, !conversation.isArchived)
                }
            },
            onMuteClick = {
                showMuteDialog = true
            },
            onPinClick = {
                selectedConversation?.let { conversation ->
                    onPinChat(conversation.id, !conversation.isPinned)
                }
            },
            onDeleteClick = {
                showDeleteConfirmation = true
            }
        )
    }

    // Mute dialog
    if (showMuteDialog && selectedConversation != null) {
        MuteChatDialog(
            isLoading = chatUiState.conversationListState.muteOperation.isInProgress,
            onDismiss = { showMuteDialog = false },
            onMute = {
                selectedConversation?.let { conversation ->
                    onMuteChat(conversation.id, !conversation.isMuted)
                }
                showMuteDialog = false
            }
        )
    }

    // Delete confirmation dialog
    if (showDeleteConfirmation && selectedConversation != null) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = { Text(stringResource(R.string.delete_chat_dialog_title)) },
            text = { Text(stringResource(R.string.delete_chat_dialog_message)) },
            confirmButton = {
                Button(
                    onClick = {
                        // Delete chat functionality will be implemented later
                        showDeleteConfirmation = false
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text(stringResource(R.string.delete_button))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirmation = false }
                ) {
                    Text(stringResource(R.string.cancel_button))
                }
            }
        )
    }

    DisposableEffect(Unit) {
        onSetTopAppBar {
            MeenaTopBar(
                title = stringResource(R.string.chats_screen_title),
                onSearchClick = onNavigateToSearch,
                actions = {
                    // Toggle archived chats
                    IconButton(
                        onClick = {
                            onToggleShowArchivedChats(!chatUiState.showArchivedChats)
                        }
                    ) {
                        Icon(
                            imageVector = if (chatUiState.showArchivedChats)
                                Icons.Default.Unarchive else Icons.Default.Archive,
                            contentDescription = if (chatUiState.showArchivedChats)
                                stringResource(R.string.show_active_chats_content_description) else stringResource(R.string.show_archived_chats_content_description)
                        )
                    }

                    // Refresh button
                    IconButton(onClick = { onLoadChats() }) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = stringResource(R.string.refresh_content_description)
                        )
                    }
                }
            )
        }
        onSetFab(
            {
                // FAB with menu
                var showFabMenu by remember { mutableStateOf(false) }

                Column(horizontalAlignment = Alignment.End) {
                    // FAB menu
                    if (showFabMenu) {
                        // New Group FAB
                        FloatingActionButton(
                            onClick = {
                                showFabMenu = false
                                onNavigateToCreateGroup()
                            },
                            containerColor = MaterialTheme.colorScheme.secondaryContainer,
                            contentColor = MaterialTheme.colorScheme.onSecondaryContainer,
                            modifier = Modifier.padding(vertical = 8.dp)
                        ) {
                            Row(
                                modifier = Modifier.padding(horizontal = 16.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.GroupAdd,
                                    contentDescription = stringResource(R.string.new_group_content_description)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(stringResource(R.string.new_group_button))
                            }
                        }

                        // New Chat FAB
                        FloatingActionButton(
                            onClick = {
                                showFabMenu = false
                                onNavigateToNewChat()
                            },
                            containerColor = MaterialTheme.colorScheme.secondaryContainer,
                            contentColor = MaterialTheme.colorScheme.onSecondaryContainer,
                            modifier = Modifier.padding(vertical = 8.dp)
                        ) {
                            Row(
                                modifier = Modifier.padding(horizontal = 16.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.AutoMirrored.Filled.Chat,
                                    contentDescription = stringResource(R.string.new_chat_content_description)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(stringResource(R.string.new_chat_button))
                            }
                        }
                    }

                    // Main FAB
                    FloatingActionButton(
                        onClick = { showFabMenu = !showFabMenu },
                        containerColor = MaterialTheme.colorScheme.primary,
                        contentColor = Color.White
                    ) {
                        Icon(
                            imageVector = if (showFabMenu) Icons.Default.Close else Icons.Default.Add,
                            contentDescription = if (showFabMenu) stringResource(R.string.close_menu_content_description) else stringResource(R.string.open_menu_content_description)
                        )
                    }
                }
            },
            FabPosition.End
        )
        onDispose {
            // Clear the TopAppBar and FAB when leaving this screen
            onSetTopAppBar(null)
            onSetBottomAppBar(null)
            onSetFab(null, FabPosition.End)
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        if (chats.isEmpty() && !chatUiState.properties.isLoading) {
            // Empty state
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.Chat,
                    contentDescription = null, // This is decorative icon, contentDescription can be null
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = stringResource(R.string.no_chats_headline),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold
                    )
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = stringResource(R.string.start_new_conversation_prompt),
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )
            }
        } else {
            // Conversation type tabs
            Column(modifier = Modifier.fillMaxSize()) {
                ConversationTypeTabs(
                    selectedTabIndex = selectedTabIndex,
                    onTabSelected = { index, type ->
                        selectedTabIndex = index
                        selectedConversationType = type
                    },
                    oneToOneCount = getOneToOneConversations().size,
                    groupCount = getGroupConversations().size,
                    channelCount = getChannelConversations().size
                )

                // Filtered conversation list
                val filteredConversations = when (selectedConversationType) {
                    ConversationType.ONE_TO_ONE -> getOneToOneConversations()
                    ConversationType.GROUP -> getGroupConversations()
                    ConversationType.CHANNEL -> getChannelConversations()
                }

                if (filteredConversations.isEmpty()) {
                    // Empty state for the selected type
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        val (icon, messageResId) = when (selectedConversationType) {
                            ConversationType.ONE_TO_ONE -> Icons.AutoMirrored.Filled.Chat to R.string.no_direct_messages
                            ConversationType.GROUP -> Icons.Default.Group to R.string.no_groups
                            ConversationType.CHANNEL -> Icons.Default.Public to R.string.no_channels
                        }

                        Icon(
                            imageVector = icon,
                            contentDescription = null, // This is decorative icon, contentDescription can be null
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = stringResource(messageResId),
                            style = MaterialTheme.typography.headlineSmall.copy(
                                fontWeight = FontWeight.Bold
                            )
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = stringResource(R.string.start_new_conversation_prompt),
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center
                        )
                    }
                } else {
                    // Conversation list
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(filteredConversations) { conversation ->
                            TelegramChatListItem(
                                chatName = conversation.name,
                                lastMessage = conversation.lastMessage ?: stringResource(R.string.no_messages_yet),
                                lastMessageTime = conversation.lastMessageTimestamp?.let {
                                    SimpleDateFormat("HH:mm", Locale.getDefault()).format(Date(it))
                                } ?: "",
                                avatarUrl = conversation.avatarUrl,
                                unreadCount = conversation.unreadCount,
                                isPinned = conversation.isPinned,
                                isMuted = conversation.isMuted,
                                isGroup = conversation.conversationType == ConversationType.GROUP,
                                isChannel = conversation.conversationType == ConversationType.CHANNEL,
                                onItemClick = { onNavigateToChat(conversation.id) },
                                onLongClick = {
                                    selectedConversation = conversation
                                    showChatOptions = true
                                },
                                onPinClick = {
                                    onPinChat(conversation.id, !conversation.isPinned)
                                },
                                onMuteClick = {
                                    onMuteChat(conversation.id, !conversation.isMuted)
                                },
                                onDeleteClick = {
                                    selectedConversation = conversation
                                    showDeleteConfirmation = true
                                },
                                onArchiveClick = {
                                    onArchiveChat(conversation.id, !conversation.isArchived)
                                }
                            )
                        }
                    }
                }
            }
        }

        if (chatUiState.properties.isLoading ||
            chatUiState.conversationListState.archiveOperation.isInProgress ||
            chatUiState.conversationListState.muteOperation.isInProgress ||
            chatUiState.conversationListState.pinOperation.isInProgress) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}