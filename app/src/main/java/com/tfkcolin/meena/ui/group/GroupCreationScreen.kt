package com.tfkcolin.meena.ui.group

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Group
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Public
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import kotlinx.coroutines.launch
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.PrivacyType
import com.tfkcolin.meena.ui.chat.ChatViewModel
import com.tfkcolin.meena.ui.components.ContactSelectionDialog
import com.tfkcolin.meena.ui.components.ErrorSnackbar

/**
 * Screen for creating a new group.
 *
 * @param onNavigateBack Navigate back to the previous screen.
 * @param onNavigateToChat Navigate to the chat screen.
 * @param viewModel The chat view model.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GroupCreationScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onShowSnackbar: (String) -> Unit,
    onNavigateBack: () -> Unit,
    onNavigateToChat: (String) -> Unit,
    isCreatingGroup: Boolean,
    isGroupCreated: Boolean,
    newGroupChatId: String?,
    error: String?,
    onClearError: () -> Unit,
    onCreateGroup: (
        name: String,
        description: String?,
        initialMembers: List<String>,
        privacyType: String
    ) -> Unit,
    onResetOperationStates: () -> Unit
) {
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()

    // Group creation state
    var groupName by remember { mutableStateOf("") }
    var groupDescription by remember { mutableStateOf("") }
    var selectedPrivacyType by remember { mutableStateOf(PrivacyType.PRIVATE) }
    var showContactSelection by remember { mutableStateOf(false) }
    var selectedContacts by remember { mutableStateOf(listOf<String>()) }

    // Navigate to chat when group is created
    LaunchedEffect(isGroupCreated, newGroupChatId) {
        if (isGroupCreated && newGroupChatId != null) {
            onNavigateToChat(newGroupChatId)
            onResetOperationStates()
        }
    }

    LaunchedEffect(error) {
        error?.let {
            onShowSnackbar(it)
            onClearError()
        }
    }
//    // Show error message
//    ErrorSnackbar(
//        errorMessage = error,
//        snackbarHostState = snackbarHostState,
//        scope = scope,
//        onDismiss = onClearError
//    )

    // Contact selection dialog
    if (showContactSelection) {
        ContactSelectionDialog(
            onDismiss = { showContactSelection = false },
            onContactsSelected = { contacts ->
                selectedContacts = contacts
                showContactSelection = false
            },
            initialSelectedContacts = selectedContacts
        )
    }

    val groupNameError = stringResource(R.string.group_name_cannot_be_empty)

    DisposableEffect(Unit) {
        onSetTopAppBar {
            TopAppBar(
                title = { Text(stringResource(R.string.create_group)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back_icon_description)
                        )
                    }
                },
                actions = {
                    // Create group button
                    if (!isCreatingGroup) {
                        IconButton(
                            onClick = {
                                if (groupName.isNotBlank()) {
                                    onCreateGroup(
                                        groupName,
                                        groupDescription.takeIf { it.isNotBlank() },
                                        selectedContacts,
                                        selectedPrivacyType.value
                                    )
                                } else {
                                    // Show error for empty group name using snackbar
                                    scope.launch {
                                        snackbarHostState.showSnackbar(groupNameError)
                                    }
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = stringResource(R.string.create_group_icon_description)
                            )
                        }
                    } else {
                        // Show loading indicator
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    }
                }
            )
        }
        onDispose {
            // Clear the TopAppBar and FAB when leaving this screen
            onSetTopAppBar(null)
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Group avatar
            Box(
                modifier = Modifier
                    .size(100.dp)
                    .clip(CircleShape)
                    .align(Alignment.CenterHorizontally)
                    .background(MaterialTheme.colorScheme.surfaceVariant),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Camera,
                    contentDescription = stringResource(R.string.add_group_photo),
                    modifier = Modifier.size(40.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Group name
            OutlinedTextField(
                value = groupName,
                onValueChange = { groupName = it },
                label = { Text(stringResource(R.string.group_name_label)) },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                isError = groupName.isBlank() && error != null
            )

            // Group description
            OutlinedTextField(
                value = groupDescription,
                onValueChange = { groupDescription = it },
                label = { Text(stringResource(R.string.group_description_label_optional)) },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
                maxLines = 3
            )

            // Privacy type selection
            Text(
                text = stringResource(R.string.privacy_type_title),
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(top = 8.dp)
            )

            PrivacyTypeOption(
                title = stringResource(R.string.public_chat),
                description = stringResource(R.string.public_group_description),
                icon = Icons.Default.Public,
                isSelected = selectedPrivacyType == PrivacyType.PUBLIC,
                onClick = { selectedPrivacyType = PrivacyType.PUBLIC }
            )

            PrivacyTypeOption(
                title = stringResource(R.string.private_chat),
                description = stringResource(R.string.private_group_description),
                icon = Icons.Default.Group,
                isSelected = selectedPrivacyType == PrivacyType.PRIVATE,
                onClick = { selectedPrivacyType = PrivacyType.PRIVATE }
            )

            PrivacyTypeOption(
                title = stringResource(R.string.secret),
                description = stringResource(R.string.secret_group_description),
                icon = Icons.Default.Lock,
                isSelected = selectedPrivacyType == PrivacyType.SECRET,
                onClick = { selectedPrivacyType = PrivacyType.SECRET }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Add members button
            Button(
                onClick = { showContactSelection = true },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (selectedContacts.isEmpty())
                        stringResource(R.string.add_members_button)
                    else
                        stringResource(R.string.add_members_with_count, selectedContacts.size)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        // Loading indicator
        if (isCreatingGroup) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

/**
 * Privacy type option component.
 *
 * @param title The title of the privacy type.
 * @param description The description of the privacy type.
 * @param icon The icon for the privacy type.
 * @param isSelected Whether this privacy type is selected.
 * @param onClick The click handler.
 */
@Composable
fun PrivacyTypeOption(
    title: String,
    description: String,
    icon: ImageVector,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clickable(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        RadioButton(
            selected = isSelected,
            onClick = onClick
        )

        Icon(
            imageVector = icon,
            contentDescription = title,
            modifier = Modifier.padding(horizontal = 8.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )

            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
