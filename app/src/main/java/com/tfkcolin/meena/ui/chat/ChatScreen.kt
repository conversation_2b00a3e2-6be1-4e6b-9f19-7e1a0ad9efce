package com.tfkcolin.meena.ui.chat

import android.util.Log
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.AttachFile
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Send
import android.widget.Toast
import androidx.compose.ui.platform.LocalContext
import com.tfkcolin.meena.ui.chat.components.MediaSelector
import java.util.UUID
import com.tfkcolin.meena.ui.chat.components.ChatInput
import com.tfkcolin.meena.ui.chat.components.MessageItem
import com.tfkcolin.meena.ui.chat.components.TypingIndicator
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FabPosition
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.ui.components.AttachmentSelector
import com.tfkcolin.meena.ui.components.DeleteMessageDialog
import com.tfkcolin.meena.ui.components.MediaAttachmentGrid
import com.tfkcolin.meena.ui.components.MediaAttachmentView
import com.tfkcolin.meena.ui.components.MediaPreviewScreen
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.components.MessageEditDialog
import com.tfkcolin.meena.ui.components.MessageOptionsMenu
import com.tfkcolin.meena.ui.components.MessageReactionsView
import com.tfkcolin.meena.ui.components.MessageReplyView
import com.tfkcolin.meena.ui.components.MessageStatusIndicator
import com.tfkcolin.meena.ui.components.ReactionPicker
import com.tfkcolin.meena.data.models.MessageReactionSummary
import com.tfkcolin.meena.ui.models.ConversationListItem
import com.tfkcolin.meena.ui.navigation.Screen
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject
import kotlin.collections.find

/**
 * Chat screen.
 *
 * @param chatId The chat ID.
 * @param recipientId The recipient ID.
 * @param onNavigateBack Navigate back to the chat list screen.
 */
@Composable
fun ChatScreen(
    onSetTopAppBar: (@Composable (() -> Unit)?) -> Unit,
    onSetBottomAppBar: (@Composable (() -> Unit)?) -> Unit,
    onShowSnackbar: (String) -> Unit,
    chatId: String,
    recipientId: String,
    onNavigateBack: () -> Unit,
    onNavigateToSearch: (String, String) -> Unit,
    onNavigateToForward: (String) -> Unit,
    navController: NavController,
    chatUiState: ChatUiState,
    messages: List<Message>,
    typingUsers: Map<String, Boolean>,
    mediaAttachmentHelper: MediaAttachmentHelper,
    conversationListItems: List<ConversationListItem>,
    onSendTypingIndicator: (Boolean) -> Unit,
    onSetCurrentChat: (String) -> Unit,
    onClearChatError: () -> Unit,
    onResetAllOperationStates: () -> Unit,
    onClearMessageToEdit: () -> Unit,
    onEditMessage: (String, String) -> Unit,
    onDeleteMessageForSelf: (String) -> Unit,
    onDeleteMessageForEveryone: (String) -> Unit,
    onGetCurrentUserId: () -> String,
    onSetMessageToEdit: (Message) -> Unit,
    onSetMessageToReplyTo: (Message) -> Unit,
    onToggleReaction: (String, String, String) -> Unit,
    onAddAttachment: (MediaAttachment) -> Unit,
    onRemoveAttachment: (MediaAttachment) -> Unit,
    onClearAttachments: () -> Unit,
    onReplyToMessageWithAttachments: (String, String, String, List<MediaAttachment>) -> Unit,
    onReplyToMessage: (String, String, String) -> Unit,
    onSendMessageWithAttachments: (String, String, List<MediaAttachment>) -> Unit,
    onSendMessage: (String, String) -> Unit,
    onGetMessageById: (String) -> Message?
) {
    val snackbarHostState = remember { SnackbarHostState() }
    val focusManager = LocalFocusManager.current
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()

    var messageText by remember { mutableStateOf("") }
    var isTyping by remember { mutableStateOf(false) }

    // Send typing indicator when text changes
    LaunchedEffect(messageText) {
        if (messageText.isNotEmpty() && !isTyping) {
            isTyping = true
            onSendTypingIndicator(true)
        } else if (messageText.isEmpty() && isTyping) {
            isTyping = false
            onSendTypingIndicator(false)
        }
    }
    var showAttachmentSelector by remember { mutableStateOf(false) }
    var showMediaSelector by remember { mutableStateOf(false) }
    val context = LocalContext.current
    var selectedMessage by remember { mutableStateOf<Message?>(null) }
    var showMessageOptions by remember { mutableStateOf(false) }
    var showDeleteForSelfDialog by remember { mutableStateOf(false) }
    var showDeleteForEveryoneDialog by remember { mutableStateOf(false) }
    var showReactionPicker by remember { mutableStateOf(false) }

    // Try to find the conversation name from conversationListItems
    val recipientDisplayName = remember(conversationListItems, chatId) {
        conversationListItems.find { it.id == chatId }?.name ?: recipientId
    }

    // Set current chat and load messages
    LaunchedEffect(chatId) {
        onSetCurrentChat(chatId)
    }

    // Show error message
    LaunchedEffect(chatUiState.error) {
        chatUiState.error?.let {
            onShowSnackbar(it)
            onClearChatError()
        }
    }

    // Reset message text when message is sent
    LaunchedEffect(chatUiState.messageState.sendOperation.isSuccessful) {
        if (chatUiState.messageState.sendOperation.isSuccessful) {
            messageText = ""
            onResetAllOperationStates()
        }
    }

    // Scroll to bottom when new messages arrive
    LaunchedEffect(messages) {
        if (messages.isNotEmpty()) {
            listState.animateScrollToItem(messages.size - 1)
        }
    }

    // Handle typing indicator
    LaunchedEffect(messageText) {
        val isCurrentlyTyping = messageText.isNotEmpty()
        if (isTyping != isCurrentlyTyping) {
            isTyping = isCurrentlyTyping
            onSendTypingIndicator(isTyping)
        }
    }

    // Clean up typing indicator when leaving the screen
    DisposableEffect(Unit) {
        onSetTopAppBar {
            MeenaTopBar(
                title = recipientDisplayName,
                onBackClick = onNavigateBack,
                onSearchClick = {
                    onNavigateToSearch(chatId, recipientDisplayName)
                }
            )
        }
        onSetBottomAppBar(null)
        onDispose {
            onSetTopAppBar(null)
            onSetBottomAppBar(null)
            onSendTypingIndicator(false)
        }
    }

    // Message edit dialog
    chatUiState.messageToEdit?.let { messageToEdit ->
        MessageEditDialog(
            message = messageToEdit,
            isLoading = chatUiState.messageState.editOperation.isInProgress,
            onDismiss = { onClearMessageToEdit() },
            onSave = { newContent ->
                onEditMessage(messageToEdit.id, newContent)
            }
        )
    }

    // Delete for self dialog
    if (showDeleteForSelfDialog && selectedMessage != null) {
        DeleteMessageDialog(
            title = stringResource(R.string.delete_for_me_title),
            message = stringResource(R.string.delete_for_me_message),
            confirmButtonText = stringResource(R.string.delete_for_me_button),
            isLoading = chatUiState.messageState.deleteOperation.isInProgress,
            onDismiss = { showDeleteForSelfDialog = false },
            onConfirm = {
                selectedMessage?.let { message ->
                    onDeleteMessageForSelf(message.id)
                }
                showDeleteForSelfDialog = false
            }
        )
    }

    // Delete for everyone dialog
    if (showDeleteForEveryoneDialog && selectedMessage != null) {
        DeleteMessageDialog(
            title = stringResource(R.string.delete_for_everyone_title),
            message = stringResource(R.string.delete_for_everyone_message),
            confirmButtonText = stringResource(R.string.delete_for_everyone_button),
            isLoading = chatUiState.messageState.deleteOperation.isInProgress,
            onDismiss = { showDeleteForEveryoneDialog = false },
            onConfirm = {
                selectedMessage?.let { message ->
                    onDeleteMessageForEveryone(message.id)
                }
                showDeleteForEveryoneDialog = false
            }
        )
    }

    // Message options menu
    if (showMessageOptions && selectedMessage != null) {
        val currentUserId = onGetCurrentUserId()
        val isFromCurrentUser = selectedMessage?.senderId == currentUserId

        MessageOptionsMenu(
            message = selectedMessage!!,
            isFromCurrentUser = isFromCurrentUser,
            isExpanded = showMessageOptions,
            onDismiss = { showMessageOptions = false },
            onCopyClick = {
                // Copy functionality will be handled in the MessageOptionsMenu
            },
            onEditClick = {
                onSetMessageToEdit(selectedMessage!!)
            },
            onDeleteForSelfClick = {
                showDeleteForSelfDialog = true
            },
            onDeleteForEveryoneClick = {
                showDeleteForEveryoneDialog = true
            },
            onReplyClick = {
                onSetMessageToReplyTo(selectedMessage!!)
            },
            onForwardClick = {
                selectedMessage?.let { message ->
                    onNavigateToForward(message.id)
                }
            },
            onReactClick = {
                showReactionPicker = true
            }
        )
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Messages
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = listState,
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(messages) { message ->
                    // ON One-to-one chat, the recipientId is set to default
                    val currentUserId = onGetCurrentUserId()
                    MessageItem(
                        message = message,
                        isFromCurrentUser = message.senderId == currentUserId,
                        onLongClick = { selectedMsg ->
                            selectedMessage = selectedMsg
                            showMessageOptions = true
                        },
                        navController = navController,
                        mediaAttachmentHelper = mediaAttachmentHelper,
                        currentUserId = currentUserId,
                        onToggleReaction = onToggleReaction
                    )
                }

                // Typing indicator
                val anyoneTyping = typingUsers.any { (userId, isTyping) ->
                    userId != onGetCurrentUserId() && isTyping
                }
                if (anyoneTyping) {
                    item {
                        TypingIndicator()
                    }
                }
            }

            // Message input
            ChatInput(
                text = messageText,
                onTextChange = { messageText = it },
                onSendClick = {
                    val messageToReplyTo = chatUiState.messageState.messageToReplyTo

                    if (messageToReplyTo != null) {
                        // Send reply
                        if (chatUiState.selectedAttachments.isNotEmpty()) {
                            // Reply with attachments
                            onReplyToMessageWithAttachments(
                                recipientId,
                                messageText,
                                messageToReplyTo.id,
                                chatUiState.selectedAttachments
                            )
                        } else {
                            // Text-only reply
                            onReplyToMessage(
                                recipientId,
                                messageText,
                                messageToReplyTo.id
                            )
                        }
                    } else {
                        // Send normal message
                        if (chatUiState.selectedAttachments.isNotEmpty()) {
                            // Send message with attachments
                            onSendMessageWithAttachments(
                                recipientId,
                                messageText,
                                chatUiState.selectedAttachments
                            )
                        } else if (messageText.isNotBlank()) {
                            // Send text-only message
                            onSendMessage(
                                recipientId,
                                messageText
                            )
                        }
                    }
                    focusManager.clearFocus()
                },
                onAttachmentClick = {
                    showMediaSelector = true
                },
                selectedAttachments = chatUiState.selectedAttachments,
                onClearAttachments = onClearAttachments,
                isLoading = chatUiState.messageState.sendOperation.isInProgress || chatUiState.messageState.replyOperation.isInProgress
            )

            // Attachment selector
            if (showAttachmentSelector) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.5f))
                        .clickable(
                            onClick = { showAttachmentSelector = false },
                            indication = null,
                            interactionSource = remember { MutableInteractionSource() }
                        ),
                    contentAlignment = Alignment.BottomCenter
                ) {
                    AttachmentSelector(
                        onAttachmentSelected = { attachment ->
                            onAddAttachment(attachment)
                            showAttachmentSelector = false
                        },
                        onDismiss = {
                            showAttachmentSelector = false
                        },
                        mediaAttachmentHelper = mediaAttachmentHelper
                    )
                }
            }
        }

        if (chatUiState.properties.isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }

    // Show reaction picker if needed
    if (showReactionPicker && selectedMessage != null) {
        ReactionPicker(
            onReactionSelected = { emoji ->
                val currentUserId = onGetCurrentUserId()
                onToggleReaction(selectedMessage!!.id, emoji, currentUserId)
                showReactionPicker = false
            },
            onDismiss = {
                showReactionPicker = false
            }
        )
    }

    val cameraNotImplemented = stringResource(R.string.toast_camera_not_implemented)
    val videoNotImplemented = stringResource(R.string.toast_video_not_implemented)
    val audioNotImplemented = stringResource(R.string.toast_audio_not_implemented)
    // Media selector dialog
    if (showMediaSelector) {
        MediaSelector(
            onMediaSelected = { mediaType ->
                showMediaSelector = false
                when (mediaType) {
                    "gallery" -> {
                        // Launch gallery picker
                        // This would typically use the Activity Result API
                        // For now, we'll just simulate adding an attachment
                        val mockAttachment = MediaAttachment(
                            id = UUID.randomUUID().toString(),
                            messageId = "",
                            type = "image",
                            url = "",
                            thumbnailUrl = null,
                            name = "Sample Image",
                            size = 1024L,
                            duration = null,
                            width = 800,
                            height = 600,
                            latitude = null,
                            longitude = null
                        )
                        onAddAttachment(mockAttachment)
                        // Navigate to preview screen
                        navController.navigate(
                            Screen.MediaPreview.createRoute(
                                messageId = "draft", // Use "draft" to indicate this is a new attachment
                                initialIndex = 0
                            )
                        )
                    }
                    "camera" -> {
                        // Launch camera
                        // This would typically use the Activity Result API
                        Toast.makeText(context, cameraNotImplemented, Toast.LENGTH_SHORT).show()
                    }
                    "video" -> {
                        // Launch video recorder
                        // This would typically use the Activity Result API
                        Toast.makeText(context, videoNotImplemented, Toast.LENGTH_SHORT).show()
                    }
                    "audio" -> {
                        // Launch audio recorder
                        // This would typically use the Activity Result API
                        Toast.makeText(context, audioNotImplemented, Toast.LENGTH_SHORT).show()
                    }
                }
            },
            onDismiss = { showMediaSelector = false }
        )
    }
}

/**
 * Message item component.
 *
 * @param message The message to display.
 * @param isFromCurrentUser Whether the message is from the current user.
 * @param onLongClick The callback for when the message is long-pressed.
 * @param navController The navigation controller.
 * @param mediaAttachmentHelper The media attachment helper.
 * @param modifier The modifier for the component.
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun MessageItem(
    message: Message,
    isFromCurrentUser: Boolean,
    onLongClick: (Message) -> Unit,
    navController: NavController,
    mediaAttachmentHelper: MediaAttachmentHelper,
    currentUserId: String,
    onToggleReaction: (String, String, String) -> Unit,
    modifier: Modifier = Modifier
) {
    val alignment = if (isFromCurrentUser) Alignment.End else Alignment.Start
    val backgroundColor = if (isFromCurrentUser) {
        MaterialTheme.colorScheme.primary
    } else {
        MaterialTheme.colorScheme.surfaceVariant
    }
    val textColor = if (isFromCurrentUser) {
        Color.White
    } else {
        MaterialTheme.colorScheme.onSurfaceVariant
    }
    val shape = if (isFromCurrentUser) {
        RoundedCornerShape(16.dp, 16.dp, 0.dp, 16.dp)
    } else {
        RoundedCornerShape(16.dp, 16.dp, 16.dp, 0.dp)
    }

    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = alignment
    ) {
        // If this is a reply, show the message being replied to
        if (message.isReply() && message.replyToMessage != null) {
            val replyToMessage = message.replyToMessage!!
            val senderName = if (replyToMessage.senderId == currentUserId) stringResource(R.string.message_reply_sender_yourself) else replyToMessage.senderId

            Box(
                modifier = Modifier
                    .padding(horizontal = 8.dp)
                    .align(alignment)
            ) {
                MessageReplyView(
                    replyToMessage = replyToMessage,
                    senderName = senderName,
                    onReplyClick = {
                        // Scroll to the original message (to be implemented)
                    }
                )
            }
        }

        Card(
            modifier = Modifier
                .padding(8.dp)
                .combinedClickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null,
                    onClick = { /* Do nothing on click */ },
                    onLongClick = { onLongClick(message) }
                ),
            shape = shape,
            colors = CardDefaults.cardColors(
                containerColor = backgroundColor
            )
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                // Display message content
                Text(
                    text = message.content,
                    color = textColor,
                    style = MaterialTheme.typography.bodyLarge
                )

                // Display media attachments if any
                if (message.hasAttachments && message.attachments != null && message.attachments!!.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))

                    MediaAttachmentGrid(
                        attachments = message.attachments!!,
                        onAttachmentClick = { attachment ->
                            // Find the index of the clicked attachment
                            val attachmentIndex = message.attachments!!.indexOf(attachment)
                            // Navigate to the media preview screen
                            navController.navigate(Screen.MediaPreview.createRoute(message.id, attachmentIndex))
                        },
                        mediaAttachmentHelper = mediaAttachmentHelper
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                // Format timestamp
                val timestamp = message.timestamp.let {
                    val date = Date(it)
                    val format = SimpleDateFormat("HH:mm", Locale.getDefault())
                    format.format(date)
                }

                // Status and timestamp
                if (isFromCurrentUser) {
                    // For messages sent by the current user, show status indicator
                    MessageStatusIndicator(
                        status = message.status,
                        timestamp = message.timestamp,
                        textColor = textColor,
                        modifier = Modifier.align(Alignment.End)
                    )
                } else {
                    // For received messages, just show the timestamp
                    Text(
                        text = timestamp,
                        style = MaterialTheme.typography.bodySmall,
                        color = textColor.copy(alpha = 0.7f),
                        modifier = Modifier.align(Alignment.End)
                    )
                }
            }
        }

        // Display reactions if any
        if (message.hasReactions()) {
            val reactionSummaries = message.reactions?.map { (emoji, userIds) ->
                MessageReactionSummary(
                    emoji = emoji,
                    count = userIds.size,
                    userIds = userIds
                )
            }?.sortedByDescending { it.count } ?: emptyList()

            Box(
                modifier = Modifier
                    .padding(horizontal = 8.dp)
                    .align(alignment)
            ) {
                MessageReactionsView(
                    reactions = reactionSummaries,
                    currentUserId = currentUserId,
                    onReactionClick = { emoji ->
                        onToggleReaction(message.id, emoji, currentUserId)
                    }
                )
            }
        }
    }
}

/**
 * Typing indicator component.
 *
 * @param modifier The modifier for the component.
 */
@Composable
fun TypingIndicator(
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically
    ) {
        val dotSize = 8.dp
        val dotColor = MaterialTheme.colorScheme.primary

        // Animated dots
        val coroutineScope = rememberCoroutineScope()
        var dot1Alpha by remember { mutableFloatStateOf(0.3f) }
        var dot2Alpha by remember { mutableFloatStateOf(0.3f) }
        var dot3Alpha by remember { mutableFloatStateOf(0.3f) }

        LaunchedEffect(Unit) {
            coroutineScope.launch {
                while (true) {
                    // Dot 1
                    dot1Alpha = 1f
                    delay(150)

                    // Dot 2
                    dot2Alpha = 1f
                    delay(150)

                    // Dot 3
                    dot3Alpha = 1f
                    delay(150)

                    // Reset
                    dot1Alpha = 0.3f
                    dot2Alpha = 0.3f
                    dot3Alpha = 0.3f
                    delay(300)
                }
            }
        }

        Text(
            text = stringResource(R.string.typing_indicator_text),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )

        Spacer(modifier = Modifier.width(4.dp))

        Box(
            modifier = Modifier
                .size(dotSize)
                .clip(CircleShape)
                .background(dotColor.copy(alpha = dot1Alpha))
        )

        Spacer(modifier = Modifier.width(4.dp))

        Box(
            modifier = Modifier
                .size(dotSize)
                .clip(CircleShape)
                .background(dotColor.copy(alpha = dot2Alpha))
        )

        Spacer(modifier = Modifier.width(4.dp))

        Box(
            modifier = Modifier
                .size(dotSize)
                .clip(CircleShape)
                .background(dotColor.copy(alpha = dot3Alpha))
        )
    }
}

/**
 * Message input component.
 *
 * @param value The current value of the input.
 * @param onValueChange The callback for when the value changes.
 * @param onSendClick The callback for when the send button is clicked.
 * @param isLoading Whether the message is being sent.
 * @param onAttachmentClick The callback for when the attachment button is clicked.
 * @param selectedAttachments The currently selected attachments.
 * @param messageToReplyTo The message being replied to, if any.
 * @param onCancelReply The callback for when the reply is canceled.
 * @param modifier The modifier for the component.
 */
@Composable
fun MessageInput(
    value: String,
    onValueChange: (String) -> Unit,
    onSendClick: () -> Unit,
    isLoading: Boolean,
    onAttachmentClick: () -> Unit,
    onClearAttachments: () -> Unit = {},
    selectedAttachments: List<MediaAttachment> = emptyList(),
    messageToReplyTo: Message? = null,
    onCancelReply: () -> Unit = {},
    currentUserId: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // Display message being replied to, if any
        if (messageToReplyTo != null) {
            val senderName = if (messageToReplyTo.senderId == currentUserId) stringResource(R.string.message_reply_sender_yourself) else messageToReplyTo.senderId

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier.weight(1f)
                ) {
                    MessageReplyView(
                        replyToMessage = messageToReplyTo,
                        senderName = senderName,
                        onReplyClick = { /* Do nothing */ }
                    )
                }

                IconButton(
                    onClick = onCancelReply
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = stringResource(R.string.cancel_reply_content_description),
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
        }

        // Display selected attachments if any
        if (selectedAttachments.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.attachments_selected_text, selectedAttachments.size),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.weight(1f))

                IconButton(
                    onClick = onClearAttachments
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = stringResource(R.string.clear_attachments_content_description),
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Attachment button
            IconButton(
                onClick = onAttachmentClick
            ) {
                Icon(
                    imageVector = Icons.Default.AttachFile,
                    contentDescription = stringResource(R.string.attach_file_content_description),
                    tint = MaterialTheme.colorScheme.primary
                )
            }

            // Text input
            OutlinedTextField(
                value = value,
                onValueChange = onValueChange,
                modifier = Modifier.weight(1f),
                placeholder = {
                    Text(
                        text = if (messageToReplyTo != null) stringResource(R.string.type_reply_placeholder) else stringResource(R.string.type_message_placeholder)
                    )
                },
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Send
                ),
                keyboardActions = KeyboardActions(
                    onSend = {
                        if ((value.isNotBlank() || selectedAttachments.isNotEmpty()) && !isLoading) {
                            onSendClick()
                        }
                    }
                ),
                maxLines = 5
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Send button
            IconButton(
                onClick = onSendClick,
                enabled = (value.isNotBlank() || selectedAttachments.isNotEmpty()) && !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Send,
                        contentDescription = stringResource(R.string.send_button_content_description),
                        tint = if (value.isNotBlank() || selectedAttachments.isNotEmpty())
                            MaterialTheme.colorScheme.primary
                        else
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                    )
                }
            }
        }
    }
}