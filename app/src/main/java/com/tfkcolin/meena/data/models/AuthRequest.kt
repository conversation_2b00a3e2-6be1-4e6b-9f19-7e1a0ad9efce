package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Registration request model for the /auth/register endpoint.
 */
data class RegisterRequest(
    @SerializedName("user_handle")
    val userHandle: String? = null,

    @SerializedName("password")
    val password: String,

    @SerializedName("email")
    val email: String? = null,

    @SerializedName("phone_number")
    val phoneNumber: String? = null,

    @SerializedName("recovery_pin")
    val recoveryPin: String? = null,

    @SerializedName("recovery_phrase")
    val recoveryPhrase: String? = null,

    @SerializedName("display_name")
    val displayName: String? = null
)

/**
 * Request model for user login.
 */
data class LoginRequest(
    @SerializedName("identifier")
    val identifier: String, // user_handle, email, or phone

    @SerializedName("password")
    val password: String
)

/**
 * Request model for token refresh.
 */
data class RefreshTokenRequest(
    @SerializedName("refresh_token")
    val refreshToken: String
)

/**
 * Request model for logout.
 */
data class LogoutRequest(
    @SerializedName("refresh_token")
    val refreshToken: String
)

/**
 * Request model for password change.
 */
data class ChangePasswordRequest(
    @SerializedName("current_password")
    val currentPassword: String,

    @SerializedName("new_password")
    val newPassword: String
)

/**
 * Request model for initiating account recovery.
 */
data class InitiateRecoveryRequest(
    @SerializedName("user_handle")
    val userHandle: String,

    @SerializedName("recovery_phrase")
    val recoveryPhrase: String,

    @SerializedName("recovery_pin")
    val recoveryPin: String
)

/**
 * Response model for recovery initiation.
 */
data class RecoveryResponse(
    @SerializedName("recovery_token")
    val recoveryToken: String
)

/**
 * Request model for confirming account recovery.
 */
data class ConfirmRecoveryRequest(
    @SerializedName("recovery_token")
    val recoveryToken: String,

    @SerializedName("new_password")
    val newPassword: String
)

/**
 * Two-factor authentication request model for the /auth/login/2fa endpoint.
 */
data class TwoFactorAuthRequest(
    @SerializedName("user_id")
    val userId: String,

    @SerializedName("code")
    val code: String,

    @SerializedName("method")
    val method: String = "totp" // totp, face_id, fingerprint
)

/**
 * Token refresh request model for the /auth/refresh endpoint.
 */
data class TokenRefreshRequest(
    @SerializedName("refresh_token")
    val refreshToken: String
)

/**
 * Account recovery request model for the /auth/recovery/request endpoint.
 */
data class AccountRecoveryRequest(
    @SerializedName("user_handle")
    val userHandle: String,

    @SerializedName("recovery_phrase")
    val recoveryPhrase: String,

    @SerializedName("recovery_pin")
    val recoveryPin: String,

    @SerializedName("new_password")
    val newPassword: String
)

/**
 * Recovery confirmation request model for the /auth/recovery/confirm endpoint.
 */
data class RecoveryConfirmRequest(
    @SerializedName("recovery_token")
    val recoveryToken: String,

    @SerializedName("new_password")
    val newPassword: String
)



/**
 * Password change request model for the /auth/password endpoint.
 */
data class PasswordChangeRequest(
    @SerializedName("current_password")
    val currentPassword: String,

    @SerializedName("new_password")
    val newPassword: String
)
