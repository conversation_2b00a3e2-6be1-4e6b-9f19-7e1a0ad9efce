package com.tfkcolin.meena.data.local

import androidx.room.*
import com.tfkcolin.meena.data.models.User
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for the User entity.
 */
@Dao
interface UserDao {
    
    /**
     * Insert a user into the database.
     * If the user already exists, replace it.
     * 
     * @param user The user to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: User)
    
    /**
     * Get a user by ID.
     * 
     * @param userId The user ID.
     * @return The user, or null if not found.
     */
    @Query("SELECT * FROM users WHERE userId = :userId")
    suspend fun getUserById(userId: String): User?
    
    /**
     * Get a user by handle.
     * 
     * @param userHandle The user handle.
     * @return The user, or null if not found.
     */
    @Query("SELECT * FROM users WHERE userHandle = :userHandle")
    suspend fun getUserByHandle(userHandle: String): User?
    
    /**
     * Get the current user.
     * 
     * @param userId The current user ID.
     * @return A flow of the current user.
     */
    @Query("SELECT * FROM users WHERE userId = :userId")
    fun getCurrentUserFlow(userId: String): Flow<User?>
    
    /**
     * Update a user's profile information.
     * 
     * @param userId The user ID.
     * @param displayName The display name.
     * @param profilePictureUrl The profile picture URL.
     * @param bio The bio.
     */
    @Query("UPDATE users SET displayName = :displayName, profilePictureUrl = :profilePictureUrl, bio = :bio WHERE userId = :userId")
    suspend fun updateUserProfile(
        userId: String,
        displayName: String?,
        profilePictureUrl: String?,
        bio: String?
    )
    
    /**
     * Delete a user by ID.
     * 
     * @param userId The user ID.
     */
    @Query("DELETE FROM users WHERE userId = :userId")
    suspend fun deleteUser(userId: String)
    
    /**
     * Delete all users.
     */
    @Query("DELETE FROM users")
    suspend fun deleteAllUsers()

    /**
     * Get all users.
     *
     * @return A list of all users.
     */
    @Query("SELECT * FROM users")
    suspend fun getAllUsers(): List<User>
}
