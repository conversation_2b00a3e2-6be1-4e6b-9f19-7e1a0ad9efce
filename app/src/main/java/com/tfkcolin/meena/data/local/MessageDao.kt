package com.tfkcolin.meena.data.local

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.tfkcolin.meena.data.models.Message
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for the Message entity.
 */
@Dao
interface MessageDao {

    /**
     * Insert a message into the database.
     * If the message already exists, replace it.
     *
     * @param message The message to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessage(message: Message)

    /**
     * Insert multiple messages into the database.
     * If a message already exists, replace it.
     *
     * @param messages The messages to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessages(messages: List<Message>)

    /**
     * Update a message's status.
     *
     * @param messageId The message ID.
     * @param status The new status.
     */
    @Query("UPDATE messages SET status = :status WHERE id = :messageId")
    suspend fun updateMessageStatus(messageId: String, status: String)

    /**
     * Update a message's content.
     *
     * @param messageId The message ID.
     * @param content The new content.
     */
    @Query("UPDATE messages SET content = :content, isEdited = 1 WHERE id = :messageId")
    suspend fun updateMessageContent(messageId: String, content: String)

    /**
     * Get a message by ID.
     *
     * @param messageId The message ID.
     * @return The message, or null if not found.
     */
    @Query("SELECT * FROM messages WHERE id = :messageId")
    suspend fun getMessageById(messageId: String): Message?

    /**
     * Get all messages for a chat.
     *
     * @param chatId The chat ID.
     * @return A flow of all messages for the chat, ordered by timestamp.
     */
    @Query("SELECT * FROM messages WHERE chatId = :chatId ORDER BY timestamp ASC")
    fun getMessagesForChatFlow(chatId: String): Flow<List<Message>>

    /**
     * Get all messages for a chat.
     *
     * @param chatId The chat ID.
     * @return All messages for the chat, ordered by timestamp.
     */
    @Query("SELECT * FROM messages WHERE chatId = :chatId ORDER BY timestamp ASC")
    suspend fun getMessagesForChat(chatId: String): List<Message>

    /**
     * Get the latest message for a chat.
     *
     * @param chatId The chat ID.
     * @return The latest message for the chat, or null if there are no messages.
     */
    @Query("SELECT * FROM messages WHERE chatId = :chatId ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestMessageForChat(chatId: String): Message?

    /**
     * Get the count of unread messages for a chat.
     *
     * @param chatId The chat ID.
     * @param userId The user ID.
     * @return The count of unread messages.
     */
    @Query("SELECT COUNT(*) FROM messages WHERE chatId = :chatId AND recipientId = :userId AND status != 'read'")
    suspend fun getUnreadMessageCountForChat(chatId: String, userId: String): Int

    /**
     * Get all unread messages for a chat.
     *
     * @param chatId The chat ID.
     * @param userId The user ID.
     * @return All unread messages for the chat.
     */
    @Query("SELECT * FROM messages WHERE chatId = :chatId AND recipientId = :userId AND status != 'read'")
    suspend fun getUnreadMessagesForChat(chatId: String, userId: String): List<Message>

    /**
     * Mark a message as deleted for self.
     *
     * @param messageId The message ID.
     */
    @Query("UPDATE messages SET deletedFor = 'self' WHERE id = :messageId")
    suspend fun markMessageAsDeletedForSelf(messageId: String)

    /**
     * Mark a message as deleted for everyone.
     *
     * @param messageId The message ID.
     */
    @Query("UPDATE messages SET deletedFor = 'everyone' WHERE id = :messageId")
    suspend fun markMessageAsDeletedForEveryone(messageId: String)

    /**
     * Permanently delete a message.
     *
     * @param messageId The message ID.
     */
    @Query("DELETE FROM messages WHERE id = :messageId")
    suspend fun deleteMessage(messageId: String)

    /**
     * Delete all messages for a chat.
     *
     * @param chatId The chat ID.
     */
    @Query("DELETE FROM messages WHERE chatId = :chatId")
    suspend fun deleteMessagesForChat(chatId: String)

    /**
     * Search for messages across all chats.
     *
     * @param query The search query.
     * @return A list of messages matching the query.
     */
    @Query("SELECT * FROM messages WHERE content LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    suspend fun searchMessages(query: String): List<Message>

    /**
     * Search for messages in a specific chat.
     *
     * @param chatId The chat ID.
     * @param query The search query.
     * @return A list of messages matching the query in the specified chat.
     */
    @Query("SELECT * FROM messages WHERE chatId = :chatId AND content LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    suspend fun searchMessagesInChat(chatId: String, query: String): List<Message>

    /**
     * Delete all messages.
     */
    @Query("DELETE FROM messages")
    suspend fun deleteAllMessages()
}
